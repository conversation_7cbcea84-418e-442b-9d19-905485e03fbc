// app/dashboard/campaigns/[id]/targeting/page.tsx
"use client";

import { CountrySelector } from "@/components/country-selector";
import { PageTypeSelector } from "@/components/page-type-selector";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Save } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Campaign {
	id: number;
	name: string;
	status: string;
}

export default function CampaignTargetingPage() {
	const router = useRouter();
	const params = useParams();
	const { toast } = useToast();
	const campaignId = params.id as string;

	const [campaign, setCampaign] = useState<Campaign | null>(null);
	const [loading, setLoading] = useState(true);
	const [saving, setSaving] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Targeting state
	const [targetingMode, setTargetingMode] = useState<"all" | "include" | "exclude">("all");
	const [includedCountries, setIncludedCountries] = useState<string[]>([]);
	const [excludedCountries, setExcludedCountries] = useState<string[]>([]);
	const [selectedPageTypes, setSelectedPageTypes] = useState<string[]>([]);
	const [selectedCategories, setSelectedCategories] = useState<Record<string, string[]>>({});
	const [selectedDevices, setSelectedDevices] = useState<string[]>([]);
	const [selectedLanguages, setSelectedLanguages] = useState<string[]>([]);
	const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
	const [selectedAgeRanges, setSelectedAgeRanges] = useState<string[]>([]);

	// Predefined age ranges
	const ageRanges = [
		{ min: 18, max: 24, name: "18-24" },
		{ min: 25, max: 34, name: "25-34" },
		{ min: 35, max: 44, name: "35-44" },
		{ min: 45, max: 54, name: "45-54" },
		{ min: 55, max: 64, name: "55-64" },
		{ min: 65, max: null, name: "65+" },
	];

	useEffect(() => {
		if (campaignId) {
			fetchCampaignAndTargeting();
		}
	}, [campaignId]);

	const handlePageTypeChange = (types: string[]) => {
		setSelectedPageTypes(types);
		// Clear categories for deselected types
		setSelectedCategories((prev) => {
			const updated = { ...prev };
			Object.keys(updated).forEach((type) => {
				if (!types.includes(type)) {
					delete updated[type];
				}
			});
			return updated;
		});
	};

	const fetchCampaignAndTargeting = async () => {
		try {
			setLoading(true);

			// Fetch campaign details
			const campaignResponse = await fetch(`/api/user/campaigns/${campaignId}`);
			if (!campaignResponse.ok) {
				throw new Error(`Failed to fetch campaign: ${campaignResponse.status}`);
			}

			const campaignResult = await campaignResponse.json();
			if (!campaignResult.success) {
				throw new Error(campaignResult.message || "Failed to fetch campaign");
			}

			const campaignData = campaignResult.data;
			setCampaign(campaignData);

			// Fetch campaign targeting data
			const targetingResponse = await fetch(`/api/user/campaigns/${campaignId}/targeting`);
			if (targetingResponse.ok) {
				const targetingResult = await targetingResponse.json();
				if (targetingResult.success && targetingResult.data) {
					const targeting = targetingResult.data;

					// Parse targeting data
					if (targeting.countries) {
						if (targeting.countries.mode === "include") {
							setTargetingMode("include");
							setIncludedCountries(targeting.countries.include || []);
						} else if (targeting.countries.mode === "exclude") {
							setTargetingMode("exclude");
							setExcludedCountries(targeting.countries.exclude || []);
						} else {
							setTargetingMode("all");
						}
					}

					if (targeting.pageTypes) {
						// Filter out invalid page types like "all"
						const validTypes = (targeting.pageTypes.types || []).filter((type: string) =>
							["subnet", "companies", "products", "news"].includes(type)
						);
						setSelectedPageTypes(validTypes);
						setSelectedCategories(targeting.pageTypes.categories || {});
					}

					if (targeting.devices) {
						setSelectedDevices(targeting.devices || []);
					}

					if (targeting.languages) {
						setSelectedLanguages(targeting.languages || []);
					}

					if (targeting.interests) {
						setSelectedInterests(targeting.interests || []);
					}

					if (targeting.age && Array.isArray(targeting.age)) {
						setSelectedAgeRanges(targeting.age);
					}
				}
			}
		} catch (error) {
			console.error("Error fetching campaign and targeting:", error);
			setError(error instanceof Error ? error.message : "Failed to fetch campaign");
		} finally {
			setLoading(false);
		}
	};

	const handleSave = async () => {
		if (!campaign) return;

		try {
			setSaving(true);

			// Prepare targeting data
			const targetingData = {
				countries: {
					mode: targetingMode,
					include: targetingMode === "include" ? includedCountries : [],
					exclude: targetingMode === "exclude" ? excludedCountries : [],
				},
				pageTypes: {
					types: selectedPageTypes,
					categories: selectedCategories,
				},
				devices: selectedDevices,
				languages: selectedLanguages,
				interests: selectedInterests,
				age: selectedAgeRanges,
			};

			const response = await fetch(`/api/user/campaigns/${campaignId}/targeting`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(targetingData),
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Failed to update targeting: ${errorText}`);
			}

			const result = await response.json();
			if (result.success) {
				toast({
					title: "Campaign Targeting Updated",
					description:
						"Campaign targeting has been successfully updated. All ads in this campaign will use these targeting settings.",
				});
				router.push(`/dashboard/campaigns/${campaignId}`);
			} else {
				throw new Error(result.message || "Failed to update targeting");
			}
		} catch (error) {
			console.error("Error updating targeting:", error);
			toast({
				title: "Update Failed",
				description: error instanceof Error ? error.message : "Failed to update targeting",
				variant: "destructive",
			});
		} finally {
			setSaving(false);
		}
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
					<p className="mt-4 text-gray-600 dark:text-gray-400">Loading campaign...</p>
				</div>
			</div>
		);
	}

	if (error || !campaign) {
		return (
			<div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
				<div className="text-center">
					<p className="text-red-600 mb-4">{error || "Campaign not found"}</p>
					<Button onClick={() => router.push("/dashboard/campaigns")}>Back to Campaigns</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
			<div className="max-w-4xl mx-auto px-4">
				<div className="mb-6">
					<Button variant="ghost" onClick={() => router.back()} className="mb-4">
						<ArrowLeft className="mr-2 h-4 w-4" />
						Back to Campaign
					</Button>
					<div className="flex items-center justify-between">
						<div>
							<h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Campaign Targeting</h1>
							<p className="text-gray-600 dark:text-gray-400">
								Configure targeting settings for campaign "{campaign.name}". These settings apply to all
								ads in this campaign.
							</p>
						</div>
					</div>
				</div>

				<div className="space-y-6">
					{/* Geographic Targeting */}
					<Card>
						<CardHeader>
							<CardTitle>Geographic Targeting</CardTitle>
							<CardDescription>Choose which countries should see your ad</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<RadioGroup
								value={targetingMode}
								onValueChange={(v) => setTargetingMode(v as "all" | "include" | "exclude")}
								className="flex flex-col space-y-2"
							>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="all" id="all" />
									<Label htmlFor="all">Show ads to all countries</Label>
								</div>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="include" id="include" />
									<Label htmlFor="include">Include specific countries only</Label>
								</div>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="exclude" id="exclude" />
									<Label htmlFor="exclude">Exclude specific countries</Label>
								</div>
							</RadioGroup>

							{targetingMode === "include" && (
								<div className="pl-6 space-y-2">
									<Label>Countries to include</Label>
									<CountrySelector
										selected={includedCountries}
										onChange={setIncludedCountries}
										placeholder="Select countries to include..."
									/>
								</div>
							)}

							{targetingMode === "exclude" && (
								<div className="pl-6 space-y-2">
									<Label>Countries to exclude</Label>
									<CountrySelector
										selected={excludedCountries}
										onChange={setExcludedCountries}
										placeholder="Select countries to exclude..."
									/>
								</div>
							)}
						</CardContent>
					</Card>

					<Separator />

					{/* Page Type Targeting */}
					<Card>
						<CardHeader>
							<CardTitle>Page Type Targeting</CardTitle>
							<CardDescription>Choose which types of pages should display your ad</CardDescription>
						</CardHeader>
						<CardContent>
							<PageTypeSelector
								selectedTypes={selectedPageTypes}
								selectedCategories={selectedCategories}
								onTypeChange={handlePageTypeChange}
								onCategoryChange={(type, cats) =>
									setSelectedCategories((prev) => ({ ...prev, [type]: cats as string[] }))
								}
							/>
						</CardContent>
					</Card>

					<Separator />

					{/* Device Targeting */}
					<Card>
						<CardHeader>
							<CardTitle>Device Targeting</CardTitle>
							<CardDescription>Target specific device types</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{["desktop", "mobile", "tablet"].map((device) => (
									<div key={device} className="flex items-center space-x-2">
										<input
											type="checkbox"
											id={device}
											checked={selectedDevices.includes(device)}
											onChange={(e) => {
												if (e.target.checked) {
													setSelectedDevices([...selectedDevices, device]);
												} else {
													setSelectedDevices(selectedDevices.filter((d) => d !== device));
												}
											}}
											className="rounded border-gray-300 dark:border-gray-600"
										/>
										<Label htmlFor={device} className="capitalize">
											{device}
										</Label>
									</div>
								))}
							</div>
						</CardContent>
					</Card>

					<Separator />

					{/* Language Targeting */}
					<Card>
						<CardHeader>
							<CardTitle>Language Targeting</CardTitle>
							<CardDescription>Target users by their preferred language</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{["en", "es", "fr", "de", "it", "pt", "ja", "ko", "zh"].map((lang) => (
									<div key={lang} className="flex items-center space-x-2">
										<input
											type="checkbox"
											id={lang}
											checked={selectedLanguages.includes(lang)}
											onChange={(e) => {
												if (e.target.checked) {
													setSelectedLanguages([...selectedLanguages, lang]);
												} else {
													setSelectedLanguages(selectedLanguages.filter((l) => l !== lang));
												}
											}}
											className="rounded border-gray-300 dark:border-gray-600"
										/>
										<Label htmlFor={lang} className="uppercase">
											{lang}
										</Label>
									</div>
								))}
							</div>
						</CardContent>
					</Card>

					<Separator />

					{/* Interest Targeting */}
					<Card>
						<CardHeader>
							<CardTitle>Interest Targeting</CardTitle>
							<CardDescription>Target users based on their interests</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{[
									"technology",
									"business",
									"finance",
									"sports",
									"entertainment",
									"health",
									"travel",
									"education",
								].map((interest) => (
									<div key={interest} className="flex items-center space-x-2">
										<input
											type="checkbox"
											id={interest}
											checked={selectedInterests.includes(interest)}
											onChange={(e) => {
												if (e.target.checked) {
													setSelectedInterests([...selectedInterests, interest]);
												} else {
													setSelectedInterests(
														selectedInterests.filter((i) => i !== interest)
													);
												}
											}}
											className="rounded border-gray-300 dark:border-gray-600"
										/>
										<Label htmlFor={interest} className="capitalize">
											{interest}
										</Label>
									</div>
								))}
							</div>
						</CardContent>
					</Card>

					<Separator />

					{/* Age Targeting */}
					<Card>
						<CardHeader>
							<CardTitle>Age Targeting</CardTitle>
							<CardDescription>Target users by age range</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-2 gap-4">
								{ageRanges.map((range) => (
									<div key={range.name} className="flex items-center space-x-2">
										<Checkbox
											id={`age-${range.name}`}
											checked={selectedAgeRanges.includes(range.name)}
											onCheckedChange={(checked) => {
												if (checked) {
													setSelectedAgeRanges([...selectedAgeRanges, range.name]);
												} else {
													setSelectedAgeRanges(
														selectedAgeRanges.filter((r) => r !== range.name)
													);
												}
											}}
										/>
										<Label htmlFor={`age-${range.name}`} className="text-sm font-medium">
											{range.name}
										</Label>
									</div>
								))}
							</div>
						</CardContent>
					</Card>

					{/* Save Button */}
					<div className="flex justify-end">
						<Button onClick={handleSave} disabled={saving} className="min-w-[140px]">
							<Save className="mr-2 h-4 w-4" />
							{saving ? "Saving..." : "Save Campaign Targeting"}
						</Button>
					</div>
				</div>
			</div>
		</div>
	);
}
