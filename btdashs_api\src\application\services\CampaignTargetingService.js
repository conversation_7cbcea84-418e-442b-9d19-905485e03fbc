const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

/**
 * Campaign Targeting Service
 * 
 * Handles targeting rules at the campaign level.
 * All ads in a campaign inherit the campaign's targeting settings.
 * 
 * <AUTHOR> Development Team
 * @since 2.0.0
 */
class CampaignTargetingService {
	/**
	 * Create or update targeting rules for a campaign
	 * @param {number} campaignId - Campaign ID
	 * @param {Object} targetingRules - Targeting rules object
	 * @returns {Promise<Object>} Updated targeting configuration
	 */
	async setTargetingRules(campaignId, targetingRules) {
		const trx = await db.transaction();

		try {
			// Delete existing targeting rules for this campaign
			await trx("dtm_ads.campaign_targets").where({ campaign_id: campaignId }).del();

			const rules = [];

			// Process geographic targeting
			if (targetingRules.countries) {
				const { mode, include = [], exclude = [] } = targetingRules.countries;
				
				if (mode === "include" && include.length > 0) {
					for (const country of include) {
						rules.push({
							campaign_id: campaignId,
							key: "country_code",
							value: country,
							operator: "equals",
							created_at: new Date(),
							updated_at: new Date(),
						});
					}
				}
				
				if (mode === "exclude" && exclude.length > 0) {
					for (const country of exclude) {
						rules.push({
							campaign_id: campaignId,
							key: "country_exclude",
							value: country,
							operator: "equals",
							created_at: new Date(),
							updated_at: new Date(),
						});
					}
				}
			}

			// Process page type targeting
			if (targetingRules.pageTypes) {
				const { types = [], categories = {} } = targetingRules.pageTypes;
				
				// Add page types
				for (const pageType of types) {
					rules.push({
						campaign_id: campaignId,
						key: "page_type",
						value: pageType,
						operator: "equals",
						created_at: new Date(),
						updated_at: new Date(),
					});
					
					// Add categories for this page type
					const pageCategories = categories[pageType];
					if (pageCategories && pageCategories !== "all" && Array.isArray(pageCategories)) {
						for (const category of pageCategories) {
							rules.push({
								campaign_id: campaignId,
								key: "page_category",
								value: category,
								operator: "equals",
								created_at: new Date(),
								updated_at: new Date(),
							});
						}
					}
				}
			}

			// Process device targeting
			if (targetingRules.devices && targetingRules.devices.length > 0) {
				for (const device of targetingRules.devices) {
					rules.push({
						campaign_id: campaignId,
						key: "device_type",
						value: device,
						operator: "equals",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Process language targeting
			if (targetingRules.languages && targetingRules.languages.length > 0) {
				for (const language of targetingRules.languages) {
					rules.push({
						campaign_id: campaignId,
						key: "language",
						value: language,
						operator: "equals",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Process age targeting
			if (targetingRules.age) {
				if (targetingRules.age.min !== null && targetingRules.age.min !== undefined) {
					rules.push({
						campaign_id: campaignId,
						key: "age",
						value: targetingRules.age.min.toString(),
						operator: "gte",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
				
				if (targetingRules.age.max !== null && targetingRules.age.max !== undefined) {
					rules.push({
						campaign_id: campaignId,
						key: "age",
						value: targetingRules.age.max.toString(),
						operator: "lte",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Process interest targeting
			if (targetingRules.interests && targetingRules.interests.length > 0) {
				for (const interest of targetingRules.interests) {
					rules.push({
						campaign_id: campaignId,
						key: "interest",
						value: interest,
						operator: "contains",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Insert all rules
			if (rules.length > 0) {
				await trx("dtm_ads.campaign_targets").insert(rules);
			}

			// Update the campaign's targeting JSONB column
			await trx("dtm_ads.ad_campaigns")
				.where({ id: campaignId })
				.update({ 
					targeting: JSON.stringify(targetingRules),
					updated_at: new Date()
				});

			await trx.commit();

			logger.info("Campaign targeting rules updated", { 
				campaignId, 
				rulesCount: rules.length,
				targetingRules 
			});
			
			return targetingRules;
		} catch (error) {
			await trx.rollback();
			logger.error("Error setting campaign targeting rules", { 
				error, 
				campaignId, 
				targetingRules 
			});
			throw error;
		}
	}

	/**
	 * Get targeting rules for a campaign
	 * @param {number} campaignId - Campaign ID
	 * @returns {Promise<Object>} Targeting rules object
	 */
	async getTargetingRules(campaignId) {
		try {
			// First try to get from JSONB column (faster)
			const campaign = await db("dtm_ads.ad_campaigns")
				.where({ id: campaignId })
				.select("targeting")
				.first();

			if (campaign && campaign.targeting) {
				return campaign.targeting;
			}

			// Fallback: reconstruct from campaign_targets table
			const rules = await db("dtm_ads.campaign_targets")
				.where({ campaign_id: campaignId })
				.select("key", "value", "operator");

			// Group rules by key and reconstruct targeting object
			const targetingRules = {
				countries: { mode: "all", include: [], exclude: [] },
				pageTypes: { types: [], categories: {} },
				devices: [],
				languages: [],
				interests: [],
				age: { min: null, max: null }
			};

			for (const rule of rules) {
				switch (rule.key) {
					case "country_code":
						targetingRules.countries.mode = "include";
						targetingRules.countries.include.push(rule.value);
						break;
					case "country_exclude":
						targetingRules.countries.mode = "exclude";
						targetingRules.countries.exclude.push(rule.value);
						break;
					case "page_type":
						if (!targetingRules.pageTypes.types.includes(rule.value)) {
							targetingRules.pageTypes.types.push(rule.value);
							targetingRules.pageTypes.categories[rule.value] = [];
						}
						break;
					case "page_category":
						// Find which page type this category belongs to
						const pageType = targetingRules.pageTypes.types.find(type => 
							rule.value.startsWith(type + "-")
						);
						if (pageType) {
							targetingRules.pageTypes.categories[pageType].push(rule.value);
						}
						break;
					case "device_type":
						targetingRules.devices.push(rule.value);
						break;
					case "language":
						targetingRules.languages.push(rule.value);
						break;
					case "interest":
						targetingRules.interests.push(rule.value);
						break;
					case "age":
						if (rule.operator === "gte") {
							targetingRules.age.min = parseInt(rule.value);
						} else if (rule.operator === "lte") {
							targetingRules.age.max = parseInt(rule.value);
						}
						break;
				}
			}

			return targetingRules;
		} catch (error) {
			logger.error("Error getting campaign targeting rules", { error, campaignId });
			throw error;
		}
	}

	/**
	 * Check if a user/request matches campaign targeting criteria
	 * @param {number} campaignId - Campaign ID
	 * @param {Object} userContext - User context (country, device, etc.)
	 * @returns {Promise<boolean>} Whether user matches targeting
	 */
	async matchesTargeting(campaignId, userContext) {
		try {
			const targetingRules = await this.getTargetingRules(campaignId);
			
			// If no targeting rules, show to everyone
			if (!targetingRules || Object.keys(targetingRules).length === 0) {
				return true;
			}

			// Check country targeting
			if (targetingRules.countries) {
				const { mode, include, exclude } = targetingRules.countries;
				const userCountry = userContext.country_code;
				
				if (mode === "include" && include.length > 0) {
					if (!userCountry || !include.includes(userCountry)) {
						return false;
					}
				} else if (mode === "exclude" && exclude.length > 0) {
					if (userCountry && exclude.includes(userCountry)) {
						return false;
					}
				}
			}

			// Check device targeting
			if (targetingRules.devices && targetingRules.devices.length > 0) {
				const userDevice = userContext.device_type;
				if (!userDevice || !targetingRules.devices.includes(userDevice)) {
					return false;
				}
			}

			// Check language targeting
			if (targetingRules.languages && targetingRules.languages.length > 0) {
				const userLanguage = userContext.language;
				if (!userLanguage || !targetingRules.languages.includes(userLanguage)) {
					return false;
				}
			}

			// Additional targeting checks can be added here...

			return true;
		} catch (error) {
			logger.error("Error checking campaign targeting match", { 
				error, 
				campaignId, 
				userContext 
			});
			// On error, default to showing the ad
			return true;
		}
	}

	/**
	 * Get all ads that match targeting for a campaign
	 * @param {number} campaignId - Campaign ID
	 * @param {Object} userContext - User context
	 * @returns {Promise<Array>} Array of matching ads
	 */
	async getTargetedAds(campaignId, userContext) {
		try {
			const matches = await this.matchesTargeting(campaignId, userContext);
			
			if (!matches) {
				return [];
			}

			// Get all active ads for this campaign
			const ads = await db("dtm_ads.ads")
				.where({ 
					campaign_id: campaignId,
					status: "active"
				})
				.select("*");

			return ads;
		} catch (error) {
			logger.error("Error getting targeted ads", { error, campaignId, userContext });
			throw error;
		}
	}

	/**
	 * Get targeting options for UI
	 * @returns {Promise<Object>} Available targeting options
	 */
	async getTargetingOptions() {
		try {
			return {
				countries: [
					{ code: "US", name: "United States" },
					{ code: "CA", name: "Canada" },
					{ code: "UK", name: "United Kingdom" },
					{ code: "DE", name: "Germany" },
					{ code: "FR", name: "France" },
					{ code: "AU", name: "Australia" },
					{ code: "JP", name: "Japan" },
					{ code: "BR", name: "Brazil" },
					{ code: "IN", name: "India" },
					{ code: "CN", name: "China" },
				],
				devices: ["desktop", "mobile", "tablet"],
				languages: ["en", "es", "fr", "de", "pt", "ja", "zh", "hi"],
				pageTypes: ["subnet", "companies", "products", "news"],
				interests: [
					"technology", "business", "finance", "healthcare", 
					"education", "entertainment", "sports", "travel"
				],
				age_ranges: [
					{ min: 18, max: 24, name: "18-24" },
					{ min: 25, max: 34, name: "25-34" },
					{ min: 35, max: 44, name: "35-44" },
					{ min: 45, max: 54, name: "45-54" },
					{ min: 55, max: 64, name: "55-64" },
					{ min: 65, max: null, name: "65+" },
				],
			};
		} catch (error) {
			logger.error("Error getting targeting options", { error });
			throw error;
		}
	}
}

module.exports = CampaignTargetingService;
