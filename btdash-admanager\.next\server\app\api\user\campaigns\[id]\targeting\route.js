/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/campaigns/[id]/targeting/route";
exports.ids = ["app/api/user/campaigns/[id]/targeting/route"];
exports.modules = {

/***/ "(rsc)/./app/api/user/campaigns/[id]/targeting/route.ts":
/*!********************************************************!*\
  !*** ./app/api/user/campaigns/[id]/targeting/route.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_auth0__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth0 */ \"(rsc)/./lib/auth0.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n// app/api/user/campaigns/[id]/targeting/route.ts\n\n\n// GET /api/user/campaigns/[id]/targeting - Get campaign targeting\nasync function GET(request, { params }) {\n    try {\n        const paramsData = await params;\n        const campaignId = paramsData.id;\n        const { token } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_0__.auth0.getAccessToken();\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/api/auth/login\", request.url));\n        }\n        const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${campaignId}/targeting`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"API Error:\", response.status, errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: `API Error: ${response.status} - ${errorText}`\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error fetching campaign targeting:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Failed to fetch campaign targeting\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/user/campaigns/[id]/targeting - Set campaign targeting\nasync function POST(request, { params }) {\n    try {\n        const paramsData = await params;\n        const campaignId = paramsData.id;\n        const body = await request.json();\n        const { token } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_0__.auth0.getAccessToken();\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/api/auth/login\", request.url));\n        }\n        const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${campaignId}/targeting`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"API Error:\", response.status, errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: `API Error: ${response.status} - ${errorText}`\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error setting campaign targeting:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Failed to set campaign targeting\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/campaigns/[id]/targeting/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth0.ts":
/*!**********************!*\
  !*** ./lib/auth0.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth0: () => (/* binding */ auth0)\n/* harmony export */ });\n/* harmony import */ var _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth0/nextjs-auth0/server */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/index.js\");\n// lib/auth0.js\n\n// Initialize the Auth0 client\nconst auth0 = new _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__.Auth0Client({\n    session: {\n        rolling: true,\n        cookie: {\n            name: \"app_session\",\n            path: \"/\",\n            sameSite: \"lax\",\n            secure: \"development\" === \"production\"\n        }\n    },\n    authorizationParameters: {\n        scope: process.env.AUTH0_SCOPE,\n        audience: process.env.AUTH0_AUDIENCE\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aDAudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxlQUFlO0FBRTBDO0FBRXpELDhCQUE4QjtBQUN2QixNQUFNQyxRQUFRLElBQUlELG1FQUFXQSxDQUFDO0lBQ3BDRSxTQUFTO1FBQ1JDLFNBQVM7UUFDVEMsUUFBUTtZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRQyxrQkFBeUI7UUFDbEM7SUFDRDtJQUNBQyx5QkFBeUI7UUFDeEJDLE9BQU9GLFFBQVFHLEdBQUcsQ0FBQ0MsV0FBVztRQUM5QkMsVUFBVUwsUUFBUUcsR0FBRyxDQUFDRyxjQUFjO0lBQ3JDO0FBQ0QsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxsaWJcXGF1dGgwLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGxpYi9hdXRoMC5qc1xyXG5cclxuaW1wb3J0IHsgQXV0aDBDbGllbnQgfSBmcm9tIFwiQGF1dGgwL25leHRqcy1hdXRoMC9zZXJ2ZXJcIjtcclxuXHJcbi8vIEluaXRpYWxpemUgdGhlIEF1dGgwIGNsaWVudFxyXG5leHBvcnQgY29uc3QgYXV0aDAgPSBuZXcgQXV0aDBDbGllbnQoe1xyXG5cdHNlc3Npb246IHtcclxuXHRcdHJvbGxpbmc6IHRydWUsXHJcblx0XHRjb29raWU6IHtcclxuXHRcdFx0bmFtZTogXCJhcHBfc2Vzc2lvblwiLFxyXG5cdFx0XHRwYXRoOiBcIi9cIixcclxuXHRcdFx0c2FtZVNpdGU6IFwibGF4XCIsXHJcblx0XHRcdHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG5cdFx0fSxcclxuXHR9LFxyXG5cdGF1dGhvcml6YXRpb25QYXJhbWV0ZXJzOiB7XHJcblx0XHRzY29wZTogcHJvY2Vzcy5lbnYuQVVUSDBfU0NPUEUsXHJcblx0XHRhdWRpZW5jZTogcHJvY2Vzcy5lbnYuQVVUSDBfQVVESUVOQ0UsXHJcblx0fSxcclxufSk7XHJcbiJdLCJuYW1lcyI6WyJBdXRoMENsaWVudCIsImF1dGgwIiwic2Vzc2lvbiIsInJvbGxpbmciLCJjb29raWUiLCJuYW1lIiwicGF0aCIsInNhbWVTaXRlIiwic2VjdXJlIiwicHJvY2VzcyIsImF1dGhvcml6YXRpb25QYXJhbWV0ZXJzIiwic2NvcGUiLCJlbnYiLCJBVVRIMF9TQ09QRSIsImF1ZGllbmNlIiwiQVVUSDBfQVVESUVOQ0UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth0.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_user_campaigns_id_targeting_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/campaigns/[id]/targeting/route.ts */ \"(rsc)/./app/api/user/campaigns/[id]/targeting/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/campaigns/[id]/targeting/route\",\n        pathname: \"/api/user/campaigns/[id]/targeting\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/campaigns/[id]/targeting/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\api\\\\user\\\\campaigns\\\\[id]\\\\targeting\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_user_campaigns_id_targeting_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/oauth4webapi","vendor-chunks/@auth0","vendor-chunks/@edge-runtime","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Ftargeting%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();