"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/requests/[id]/page",{

/***/ "(app-pages-browser)/./app/admin/requests/[id]/page.tsx":
/*!******************************************!*\
  !*** ./app/admin/requests/[id]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdRequestReviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AdRequestReviewPage(param) {\n    let { params } = param;\n    var _campaign_targeting, _campaign_targeting_countries_include, _campaign_targeting_countries_exclude, _campaign_targeting1, _campaign_targeting_pageTypes_types, _campaign_targeting2, _campaign_targeting3, _campaign_targeting4, _campaign_targeting5;\n    _s();\n    const { id } = (0,react__WEBPACK_IMPORTED_MODULE_2__.use)(params);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [campaign, setCampaign] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [placement, setPlacement] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [advertiser, setAdvertiser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [manager, setManager] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overlappingCampaigns, setOverlappingCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AdRequestReviewPage.useEffect\": ()=>{\n            const fetchCampaignDetails = {\n                \"AdRequestReviewPage.useEffect.fetchCampaignDetails\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch campaign details from API\n                        const response = await fetch(\"/api/user/campaigns/\".concat(id));\n                        const result = await response.json();\n                        if (!result.success || !result.data) {\n                            toast({\n                                title: \"Campaign not found\",\n                                description: \"The requested campaign could not be found.\",\n                                variant: \"destructive\"\n                            });\n                            router.push(\"/admin/requests\");\n                            return;\n                        }\n                        const campaignData = result.data;\n                        setCampaign(campaignData);\n                        // For now, we'll skip overlapping campaigns check since it requires complex logic\n                        // This can be implemented later with a dedicated admin endpoint\n                        setOverlappingCampaigns([]);\n                        // Fetch placement data if available\n                        if (campaignData.slot_id) {\n                            try {\n                                const placementResponse = await fetch(\"/api/placements/\".concat(campaignData.slot_id));\n                                const placementResult = await placementResponse.json();\n                                if (placementResult.success) {\n                                    setPlacement(placementResult.data);\n                                }\n                            } catch (error) {\n                                console.log(\"Could not fetch placement data:\", error);\n                            }\n                        }\n                        // Set advertiser info from campaign data (this is the company)\n                        if (campaignData.advertiser_name) {\n                            setAdvertiser({\n                                id: campaignData.advertiser_id,\n                                name: campaignData.advertiser_name,\n                                email: campaignData.advertiser_email || \"\",\n                                role: \"company\"\n                            });\n                        }\n                        // Set manager info from campaign data (this is the user managing the campaign)\n                        if (campaignData.manager_name) {\n                            setManager({\n                                id: campaignData.manager_id,\n                                name: campaignData.manager_name,\n                                email: campaignData.manager_email || \"\",\n                                role: \"user\"\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching campaign details:\", error);\n                        toast({\n                            title: \"Error\",\n                            description: \"Failed to load campaign details. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdRequestReviewPage.useEffect.fetchCampaignDetails\"];\n            fetchCampaignDetails();\n        }\n    }[\"AdRequestReviewPage.useEffect\"], [\n        id,\n        router,\n        toast\n    ]);\n    const handleApprove = async ()=>{\n        try {\n            setSubmitting(true);\n            const response = await fetch(\"/api/admin/campaigns/\".concat(id, \"/approve\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    notes: feedback || \"Campaign approved\"\n                })\n            });\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.message || \"Failed to approve campaign\");\n            }\n            toast({\n                title: \"Campaign approved\",\n                description: \"The ad campaign has been approved and is now active.\"\n            });\n            router.push(\"/admin/requests\");\n        } catch (error) {\n            console.error(\"Error approving campaign:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to approve the campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleReject = async ()=>{\n        if (!feedback.trim()) {\n            toast({\n                title: \"Feedback required\",\n                description: \"Please provide feedback before rejecting the campaign.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const response = await fetch(\"/api/admin/campaigns/\".concat(id, \"/reject\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    reason: feedback,\n                    notes: feedback\n                })\n            });\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.message || \"Failed to reject campaign\");\n            }\n            toast({\n                title: \"Campaign rejected\",\n                description: \"The ad campaign has been rejected with feedback.\"\n            });\n            router.push(\"/admin/requests\");\n        } catch (error) {\n            console.error(\"Error rejecting campaign:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to reject the campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto flex h-[70vh] items-center justify-center p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 animate-spin text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-lg\",\n                        children: \"Loading campaign details...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n            lineNumber: 199,\n            columnNumber: 4\n        }, this);\n    }\n    if (!campaign) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Campaign not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"The requested campaign could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        className: \"mt-4\",\n                        onClick: ()=>router.push(\"/admin/requests\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 7\n                            }, this),\n                            \"Back to Requests\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n            lineNumber: 210,\n            columnNumber: 4\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        className: \"mb-4\",\n                        onClick: ()=>router.push(\"/admin/requests\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 6\n                            }, this),\n                            \"Back to Requests\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: campaign.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"outline\",\n                                                children: (placement === null || placement === void 0 ? void 0 : placement.type) || \"Unknown\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"capitalize\",\n                                                children: campaign.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"destructive\",\n                                        onClick: handleReject,\n                                        disabled: submitting,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 8\n                                            }, this),\n                                            \"Reject\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"default\",\n                                        onClick: handleApprove,\n                                        disabled: submitting,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 8\n                                            }, this),\n                                            \"Approve\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 4\n            }, this),\n            overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                variant: \"destructive\",\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertTitle, {\n                        children: \"Scheduling Conflict Detected\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                        children: [\n                            \"This campaign overlaps with \",\n                            overlappingCampaigns.length,\n                            \" existing approved campaign\",\n                            overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                            \" on the same placement.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 255,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                            defaultValue: \"overview\",\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"overview\",\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"creative\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"targeting\",\n                                            children: \"Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 8\n                                        }, this),\n                                        overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"conflicts\",\n                                            className: \"relative\",\n                                            children: [\n                                                \"Conflicts\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-white\",\n                                                    children: overlappingCampaigns.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"overview\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Campaign Overview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the campaign details and settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid gap-4 md:grid-cols-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Campaign Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Name:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 294,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: campaign.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 295,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"URL:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 300,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: campaign.url\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 301,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Duration:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 306,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: [\n                                                                                        formatDate(campaign.startDate),\n                                                                                        \" -\",\n                                                                                        \" \",\n                                                                                        formatDate(campaign.endDate)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Submitted:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 313,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: formatDate(campaign.createdAt)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 314,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Placement Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: placement ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Name:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 327,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 328,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 326,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Type:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 333,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 334,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Dimensions:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 339,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.dimensions\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 342,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Price:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 347,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.priceDisplay\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 350,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 15\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Placement information not available\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 14\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"creative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Ad Creative\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the advertisement creative and content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-hidden rounded-lg border\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"aspect-video w-full overflow-hidden bg-muted\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: campaign.imageUrl || \"/placeholder.svg\",\n                                                                        alt: campaign.name,\n                                                                        className: \"h-full w-full object-contain\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium\",\n                                                                            children: campaign.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: campaign.url\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Creative Specifications\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Format:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 393,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: (placement === null || placement === void 0 ? void 0 : placement.format) || \"Unknown\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 394,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Dimensions:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 399,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: (placement === null || placement === void 0 ? void 0 : placement.dimensions) || \"Unknown\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 400,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 398,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Max File Size:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 405,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: (placement === null || placement === void 0 ? void 0 : placement.maxFileSize) || \"Unknown\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 408,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 404,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"targeting\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Targeting Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the campaign targeting configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Geographic Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting = campaign.targeting) === null || _campaign_targeting === void 0 ? void 0 : _campaign_targeting.countries) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"Mode: \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 432,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"capitalize\",\n                                                                                    children: campaign.targeting.countries.mode\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 433,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"include\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Included Countries:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 440,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_countries_include = campaign.targeting.countries.include) === null || _campaign_targeting_countries_include === void 0 ? void 0 : _campaign_targeting_countries_include.length) > 0 ? campaign.targeting.countries.include.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: country\n                                                                                        }, country, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 447,\n                                                                                            columnNumber: 20\n                                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No countries specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 453,\n                                                                                        columnNumber: 18\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 443,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"exclude\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Excluded Countries:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 463,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_countries_exclude = campaign.targeting.countries.exclude) === null || _campaign_targeting_countries_exclude === void 0 ? void 0 : _campaign_targeting_countries_exclude.length) > 0 ? campaign.targeting.countries.exclude.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: country\n                                                                                        }, country, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 470,\n                                                                                            columnNumber: 20\n                                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No countries specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 476,\n                                                                                        columnNumber: 18\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 466,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 462,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Targeting all countries\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No geographic targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Page Type Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting1 = campaign.targeting) === null || _campaign_targeting1 === void 0 ? void 0 : _campaign_targeting1.pageTypes) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Selected Page Types:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_pageTypes_types = campaign.targeting.pageTypes.types) === null || _campaign_targeting_pageTypes_types === void 0 ? void 0 : _campaign_targeting_pageTypes_types.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: type\n                                                                                        }, type, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 509,\n                                                                                            columnNumber: 17\n                                                                                        }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No page types specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 513,\n                                                                                        columnNumber: 17\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        campaign.targeting.pageTypes.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Category Targeting:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 522,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-2 space-y-2\",\n                                                                                    children: Object.entries(campaign.targeting.pageTypes.categories).map((param)=>{\n                                                                                        let [pageType, categories] = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"rounded border p-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-sm font-medium capitalize\",\n                                                                                                    children: [\n                                                                                                        pageType,\n                                                                                                        \":\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 530,\n                                                                                                    columnNumber: 19\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                                    children: categories === \"all\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                        variant: \"outline\",\n                                                                                                        children: \"All Categories\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 535,\n                                                                                                        columnNumber: 21\n                                                                                                    }, this) : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                            variant: \"secondary\",\n                                                                                                            children: category\n                                                                                                        }, category, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                            lineNumber: 541,\n                                                                                                            columnNumber: 23\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 533,\n                                                                                                    columnNumber: 19\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, pageType, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 529,\n                                                                                            columnNumber: 18\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 525,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No page type targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Device Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting2 = campaign.targeting) === null || _campaign_targeting2 === void 0 ? void 0 : _campaign_targeting2.devices) && campaign.targeting.devices.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: device\n                                                                        }, device, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No device targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Language Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting3 = campaign.targeting) === null || _campaign_targeting3 === void 0 ? void 0 : _campaign_targeting3.languages) && campaign.targeting.languages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: language.toUpperCase()\n                                                                        }, language, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No language targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Interest Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting4 = campaign.targeting) === null || _campaign_targeting4 === void 0 ? void 0 : _campaign_targeting4.interests) && campaign.targeting.interests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.interests.map((interest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"capitalize\",\n                                                                            children: interest\n                                                                        }, interest, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No interest targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 621,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Age Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting5 = campaign.targeting) === null || _campaign_targeting5 === void 0 ? void 0 : _campaign_targeting5.age) && campaign.targeting.age.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.age.map((ageRange)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: ageRange\n                                                                        }, ageRange, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 634,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 632,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No age targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"conflicts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Scheduling Conflicts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: [\n                                                            \"This campaign overlaps with \",\n                                                            overlappingCampaigns.length,\n                                                            \" existing approved campaign\",\n                                                            overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                                                            \" on the same placement\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        overlappingCampaigns.map((overlapCampaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-2 flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-medium\",\n                                                                                children: overlapCampaign.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 666,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"capitalize\",\n                                                                                children: overlapCampaign.status\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 667,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 665,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-3 flex items-center gap-2 text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 672,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    formatDate(overlapCampaign.startDate),\n                                                                                    \" -\",\n                                                                                    \" \",\n                                                                                    formatDate(overlapCampaign.endDate)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 673,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs font-medium\",\n                                                                                        children: \"Placement\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 680,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: (placement === null || placement === void 0 ? void 0 : placement.name) || \"Unknown\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 681,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 679,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs font-medium\",\n                                                                                        children: \"Advertiser\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 684,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: (advertiser === null || advertiser === void 0 ? void 0 : advertiser.name) || \"Unknown\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 685,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 683,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 678,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, overlapCampaign.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 12\n                                                            }, this)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-900 dark:bg-yellow-950\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Conflict Resolution Options\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"ml-5 list-disc space-y-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Adjust the campaign dates to avoid overlap\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 694,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Reject this campaign with feedback about the conflict\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 695,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Approve anyway (multiple ads will rotate in the same placement)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 696,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Cancel one of the existing campaigns to make room\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"Advertiser Information (Company)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: advertiser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary\",\n                                                            children: advertiser.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: advertiser.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 723,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: advertiser.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 722,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 text-sm font-medium\",\n                                                            children: \"Company Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"ID:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: advertiser.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Type:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm capitalize\",\n                                                                    children: advertiser.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Advertiser information not available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"Campaign Manager (User)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: manager ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 text-blue-600\",\n                                                            children: manager.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: manager.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: manager.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 text-sm font-medium\",\n                                                            children: \"User Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"ID:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 767,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: manager.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Role:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 771,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm capitalize\",\n                                                                    children: manager.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Manager information not available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Campaign Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 785,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Review the campaign timeline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Campaign Duration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Start Date:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: formatDate(campaign.startDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 797,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"End Date:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: formatDate(campaign.endDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 801,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Duration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: [\n                                                                        Math.ceil((new Date(campaign.endDate).getTime() - new Date(campaign.startDate).getTime()) / (1000 * 60 * 60 * 24)),\n                                                                        \" \",\n                                                                        \"days\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 9\n                                                }, this),\n                                                overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 819,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-red-600 dark:text-red-400\",\n                                                                    children: \"Scheduling Conflict\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 818,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 dark:text-red-400\",\n                                                            children: [\n                                                                \"This campaign overlaps with \",\n                                                                overlappingCampaigns.length,\n                                                                \" existing approved campaign\",\n                                                                overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                                                                \" on the same placement.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 824,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 788,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 783,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Review Decision\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Approve or reject this ad campaign request\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 838,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"feedback\",\n                                                        className: \"mb-2 block text-sm font-medium\",\n                                                        children: \"Feedback\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                        id: \"feedback\",\n                                                        placeholder: \"Provide feedback to the advertiser...\",\n                                                        value: feedback,\n                                                        onChange: (e)=>setFeedback(e.target.value),\n                                                        className: \"min-h-[100px]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-muted-foreground\",\n                                                        children: \"Required for rejection. Optional for approval.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 840,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardFooter, {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"destructive\",\n                                                onClick: handleReject,\n                                                disabled: submitting,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 861,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    \"Reject\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 860,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"default\",\n                                                onClick: handleApprove,\n                                                disabled: submitting,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 865,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    \"Approve\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 859,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 265,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n        lineNumber: 224,\n        columnNumber: 3\n    }, this);\n}\n_s(AdRequestReviewPage, \"zwk2h5O+LfUKvekurFQ9YKEV+Mc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = AdRequestReviewPage;\nvar _c;\n$RefreshReg$(_c, \"AdRequestReviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/requests/[id]/page.tsx\n"));

/***/ })

});