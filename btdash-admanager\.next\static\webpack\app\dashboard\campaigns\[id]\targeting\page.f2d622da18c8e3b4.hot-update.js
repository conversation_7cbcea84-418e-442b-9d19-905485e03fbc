"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/campaigns/[id]/targeting/page",{

/***/ "(app-pages-browser)/./app/dashboard/campaigns/[id]/targeting/page.tsx":
/*!*********************************************************!*\
  !*** ./app/dashboard/campaigns/[id]/targeting/page.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CampaignTargetingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_country_selector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/country-selector */ \"(app-pages-browser)/./components/country-selector.tsx\");\n/* harmony import */ var _components_page_type_selector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/page-type-selector */ \"(app-pages-browser)/./components/page-type-selector.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n// app/dashboard/campaigns/[id]/targeting/page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CampaignTargetingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useParams)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const campaignId = params.id;\n    const [campaign, setCampaign] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    // Targeting state\n    const [targetingMode, setTargetingMode] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"all\");\n    const [includedCountries, setIncludedCountries] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [excludedCountries, setExcludedCountries] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [selectedPageTypes, setSelectedPageTypes] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [selectedCategories, setSelectedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)({});\n    const [selectedDevices, setSelectedDevices] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [selectedInterests, setSelectedInterests] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [selectedAgeRanges, setSelectedAgeRanges] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    // Predefined age ranges\n    const ageRanges = [\n        {\n            min: 18,\n            max: 24,\n            name: \"18-24\"\n        },\n        {\n            min: 25,\n            max: 34,\n            name: \"25-34\"\n        },\n        {\n            min: 35,\n            max: 44,\n            name: \"35-44\"\n        },\n        {\n            min: 45,\n            max: 54,\n            name: \"45-54\"\n        },\n        {\n            min: 55,\n            max: 64,\n            name: \"55-64\"\n        },\n        {\n            min: 65,\n            max: null,\n            name: \"65+\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)({\n        \"CampaignTargetingPage.useEffect\": ()=>{\n            if (campaignId) {\n                fetchCampaignAndTargeting();\n            }\n        }\n    }[\"CampaignTargetingPage.useEffect\"], [\n        campaignId\n    ]);\n    const handlePageTypeChange = (types)=>{\n        setSelectedPageTypes(types);\n        // Clear categories for deselected types\n        setSelectedCategories((prev)=>{\n            const updated = {\n                ...prev\n            };\n            Object.keys(updated).forEach((type)=>{\n                if (!types.includes(type)) {\n                    delete updated[type];\n                }\n            });\n            return updated;\n        });\n    };\n    const fetchCampaignAndTargeting = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch campaign details\n            const campaignResponse = await fetch(\"/api/user/campaigns/\".concat(campaignId));\n            if (!campaignResponse.ok) {\n                throw new Error(\"Failed to fetch campaign: \".concat(campaignResponse.status));\n            }\n            const campaignResult = await campaignResponse.json();\n            if (!campaignResult.success) {\n                throw new Error(campaignResult.message || \"Failed to fetch campaign\");\n            }\n            const campaignData = campaignResult.data;\n            setCampaign(campaignData);\n            // Fetch campaign targeting data\n            const targetingResponse = await fetch(\"/api/user/campaigns/\".concat(campaignId, \"/targeting\"));\n            if (targetingResponse.ok) {\n                const targetingResult = await targetingResponse.json();\n                if (targetingResult.success && targetingResult.data) {\n                    const targeting = targetingResult.data;\n                    // Parse targeting data\n                    if (targeting.countries) {\n                        if (targeting.countries.mode === \"include\") {\n                            setTargetingMode(\"include\");\n                            setIncludedCountries(targeting.countries.include || []);\n                        } else if (targeting.countries.mode === \"exclude\") {\n                            setTargetingMode(\"exclude\");\n                            setExcludedCountries(targeting.countries.exclude || []);\n                        } else {\n                            setTargetingMode(\"all\");\n                        }\n                    }\n                    if (targeting.pageTypes) {\n                        // Filter out invalid page types like \"all\"\n                        const validTypes = (targeting.pageTypes.types || []).filter((type)=>[\n                                \"subnet\",\n                                \"companies\",\n                                \"products\",\n                                \"news\"\n                            ].includes(type));\n                        setSelectedPageTypes(validTypes);\n                        setSelectedCategories(targeting.pageTypes.categories || {});\n                    }\n                    if (targeting.devices) {\n                        setSelectedDevices(targeting.devices || []);\n                    }\n                    if (targeting.languages) {\n                        setSelectedLanguages(targeting.languages || []);\n                    }\n                    if (targeting.interests) {\n                        setSelectedInterests(targeting.interests || []);\n                    }\n                    if (targeting.age) {\n                        setAgeRange(targeting.age || {\n                            min: null,\n                            max: null\n                        });\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Error fetching campaign and targeting:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to fetch campaign\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        if (!campaign) return;\n        try {\n            setSaving(true);\n            // Prepare targeting data\n            const targetingData = {\n                countries: {\n                    mode: targetingMode,\n                    include: targetingMode === \"include\" ? includedCountries : [],\n                    exclude: targetingMode === \"exclude\" ? excludedCountries : []\n                },\n                pageTypes: {\n                    types: selectedPageTypes,\n                    categories: selectedCategories\n                },\n                devices: selectedDevices,\n                languages: selectedLanguages,\n                interests: selectedInterests,\n                age: ageRange\n            };\n            const response = await fetch(\"/api/user/campaigns/\".concat(campaignId, \"/targeting\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(targetingData)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(\"Failed to update targeting: \".concat(errorText));\n            }\n            const result = await response.json();\n            if (result.success) {\n                toast({\n                    title: \"Campaign Targeting Updated\",\n                    description: \"Campaign targeting has been successfully updated. All ads in this campaign will use these targeting settings.\"\n                });\n                router.push(\"/dashboard/campaigns/\".concat(campaignId));\n            } else {\n                throw new Error(result.message || \"Failed to update targeting\");\n            }\n        } catch (error) {\n            console.error(\"Error updating targeting:\", error);\n            toast({\n                title: \"Update Failed\",\n                description: error instanceof Error ? error.message : \"Failed to update targeting\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600 dark:text-gray-400\",\n                        children: \"Loading campaign...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n            lineNumber: 207,\n            columnNumber: 4\n        }, this);\n    }\n    if (error || !campaign) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: error || \"Campaign not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>router.push(\"/dashboard/campaigns\"),\n                        children: \"Back to Campaigns\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n            lineNumber: 218,\n            columnNumber: 4\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: ()=>router.back(),\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 7\n                                }, this),\n                                \"Back to Campaign\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-gray-100\",\n                                        children: \"Campaign Targeting\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            'Configure targeting settings for campaign \"',\n                                            campaign.name,\n                                            '\". These settings apply to all ads in this campaign.'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Geographic Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Choose which countries should see your ad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroup, {\n                                            value: targetingMode,\n                                            onValueChange: (v)=>setTargetingMode(v),\n                                            className: \"flex flex-col space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroupItem, {\n                                                            value: \"all\",\n                                                            id: \"all\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"all\",\n                                                            children: \"Show ads to all countries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroupItem, {\n                                                            value: \"include\",\n                                                            id: \"include\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"include\",\n                                                            children: \"Include specific countries only\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroupItem, {\n                                                            value: \"exclude\",\n                                                            id: \"exclude\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"exclude\",\n                                                            children: \"Exclude specific countries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 8\n                                        }, this),\n                                        targetingMode === \"include\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Countries to include\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_country_selector__WEBPACK_IMPORTED_MODULE_1__.CountrySelector, {\n                                                    selected: includedCountries,\n                                                    onChange: setIncludedCountries,\n                                                    placeholder: \"Select countries to include...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 9\n                                        }, this),\n                                        targetingMode === \"exclude\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Countries to exclude\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_country_selector__WEBPACK_IMPORTED_MODULE_1__.CountrySelector, {\n                                                    selected: excludedCountries,\n                                                    onChange: setExcludedCountries,\n                                                    placeholder: \"Select countries to exclude...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Page Type Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Choose which types of pages should display your ad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_type_selector__WEBPACK_IMPORTED_MODULE_2__.PageTypeSelector, {\n                                        selectedTypes: selectedPageTypes,\n                                        selectedCategories: selectedCategories,\n                                        onTypeChange: handlePageTypeChange,\n                                        onCategoryChange: (type, cats)=>setSelectedCategories((prev)=>({\n                                                    ...prev,\n                                                    [type]: cats\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Device Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Target specific device types\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            \"desktop\",\n                                            \"mobile\",\n                                            \"tablet\"\n                                        ].map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: device,\n                                                        checked: selectedDevices.includes(device),\n                                                        onChange: (e)=>{\n                                                            if (e.target.checked) {\n                                                                setSelectedDevices([\n                                                                    ...selectedDevices,\n                                                                    device\n                                                                ]);\n                                                            } else {\n                                                                setSelectedDevices(selectedDevices.filter((d)=>d !== device));\n                                                            }\n                                                        },\n                                                        className: \"rounded border-gray-300 dark:border-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: device,\n                                                        className: \"capitalize\",\n                                                        children: device\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, device, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Language Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Target users by their preferred language\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            \"en\",\n                                            \"es\",\n                                            \"fr\",\n                                            \"de\",\n                                            \"it\",\n                                            \"pt\",\n                                            \"ja\",\n                                            \"ko\",\n                                            \"zh\"\n                                        ].map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: lang,\n                                                        checked: selectedLanguages.includes(lang),\n                                                        onChange: (e)=>{\n                                                            if (e.target.checked) {\n                                                                setSelectedLanguages([\n                                                                    ...selectedLanguages,\n                                                                    lang\n                                                                ]);\n                                                            } else {\n                                                                setSelectedLanguages(selectedLanguages.filter((l)=>l !== lang));\n                                                            }\n                                                        },\n                                                        className: \"rounded border-gray-300 dark:border-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: lang,\n                                                        className: \"uppercase\",\n                                                        children: lang\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, lang, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Interest Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Target users based on their interests\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            \"technology\",\n                                            \"business\",\n                                            \"finance\",\n                                            \"sports\",\n                                            \"entertainment\",\n                                            \"health\",\n                                            \"travel\",\n                                            \"education\"\n                                        ].map((interest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: interest,\n                                                        checked: selectedInterests.includes(interest),\n                                                        onChange: (e)=>{\n                                                            if (e.target.checked) {\n                                                                setSelectedInterests([\n                                                                    ...selectedInterests,\n                                                                    interest\n                                                                ]);\n                                                            } else {\n                                                                setSelectedInterests(selectedInterests.filter((i)=>i !== interest));\n                                                            }\n                                                        },\n                                                        className: \"rounded border-gray-300 dark:border-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: interest,\n                                                        className: \"capitalize\",\n                                                        children: interest\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, interest, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Age Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Target users by age range\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"minAge\",\n                                                        children: \"Minimum Age\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        id: \"minAge\",\n                                                        min: \"13\",\n                                                        max: \"100\",\n                                                        value: ageRange.min || \"\",\n                                                        onChange: (e)=>setAgeRange({\n                                                                ...ageRange,\n                                                                min: e.target.value ? parseInt(e.target.value) : null\n                                                            }),\n                                                        placeholder: \"Min age\",\n                                                        className: \"mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"maxAge\",\n                                                        children: \"Maximum Age\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        id: \"maxAge\",\n                                                        min: \"13\",\n                                                        max: \"100\",\n                                                        value: ageRange.max || \"\",\n                                                        onChange: (e)=>setAgeRange({\n                                                                ...ageRange,\n                                                                max: e.target.value ? parseInt(e.target.value) : null\n                                                            }),\n                                                        placeholder: \"Max age\",\n                                                        className: \"mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSave,\n                                disabled: saving,\n                                className: \"min-w-[140px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 8\n                                    }, this),\n                                    saving ? \"Saving...\" : \"Save Campaign Targeting\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n            lineNumber: 229,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n        lineNumber: 228,\n        columnNumber: 3\n    }, this);\n}\n_s(CampaignTargetingPage, \"ng74O/iZgPJJBwLX8KZvr6FUOyk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = CampaignTargetingPage;\nvar _c;\n$RefreshReg$(_c, \"CampaignTargetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/campaigns/[id]/targeting/page.tsx\n"));

/***/ })

});