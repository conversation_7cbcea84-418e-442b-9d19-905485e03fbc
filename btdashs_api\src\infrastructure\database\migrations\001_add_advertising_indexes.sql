-- Migration: Add indexes for advertising system performance optimization
-- File: 001_add_advertising_indexes.sql

-- Indexes for ad_campaigns table
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_manager_id ON dtm_ads.ad_campaigns(manager_id);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_advertiser_id ON dtm_ads.ad_campaigns(advertiser_id);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_status ON dtm_ads.ad_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_dates ON dtm_ads.ad_campaigns(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_created_at ON dtm_ads.ad_campaigns(created_at DESC);

-- Indexes for ads table
CREATE INDEX IF NOT EXISTS idx_ads_campaign_id ON dtm_ads.ads(campaign_id);
CREATE INDEX IF NOT EXISTS idx_ads_slot_id ON dtm_ads.ads(slot_id);
CREATE INDEX IF NOT EXISTS idx_ads_status ON dtm_ads.ads(status);
CREATE INDEX IF NOT EXISTS idx_ads_weight ON dtm_ads.ads(weight);
CREATE INDEX IF NOT EXISTS idx_ads_created_at ON dtm_ads.ads(created_at DESC);

-- Composite index for ad serving (most important for performance)
CREATE INDEX IF NOT EXISTS idx_ads_serving ON dtm_ads.ads(slot_id, status) WHERE status = 'active';

-- Indexes for ad_slots table
CREATE INDEX IF NOT EXISTS idx_ad_slots_active ON dtm_ads.ad_slots(is_active);
CREATE INDEX IF NOT EXISTS idx_ad_slots_page ON dtm_ads.ad_slots(page);

-- Indexes for ad_impressions table
CREATE INDEX IF NOT EXISTS idx_ad_impressions_ad_id ON dtm_ads.ad_impressions(ad_id);
CREATE INDEX IF NOT EXISTS idx_ad_impressions_user_id ON dtm_ads.ad_impressions(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_impressions_session_id ON dtm_ads.ad_impressions(session_id);
CREATE INDEX IF NOT EXISTS idx_ad_impressions_created_at ON dtm_ads.ad_impressions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ad_impressions_ip_address ON dtm_ads.ad_impressions(ip_address);

-- Composite index for analytics queries
CREATE INDEX IF NOT EXISTS idx_ad_impressions_analytics ON dtm_ads.ad_impressions(ad_id, created_at DESC);

-- Indexes for ad_clicks table
CREATE INDEX IF NOT EXISTS idx_ad_clicks_ad_id ON dtm_ads.ad_clicks(ad_id);
CREATE INDEX IF NOT EXISTS idx_ad_clicks_user_id ON dtm_ads.ad_clicks(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_clicks_session_id ON dtm_ads.ad_clicks(session_id);
CREATE INDEX IF NOT EXISTS idx_ad_clicks_created_at ON dtm_ads.ad_clicks(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ad_clicks_ip_address ON dtm_ads.ad_clicks(ip_address);

-- Composite index for analytics queries
CREATE INDEX IF NOT EXISTS idx_ad_clicks_analytics ON dtm_ads.ad_clicks(ad_id, created_at DESC);

-- Indexes for users table (if not already exists)
CREATE INDEX IF NOT EXISTS idx_users_auth0_user_id ON dtm_base.users(auth0_user_id);

-- Indexes for admin_users table (if not already exists)
CREATE INDEX IF NOT EXISTS idx_admin_users_auth0_user_id ON dtm_base.admin_users(auth0_user_id);

-- Add foreign key constraints (remove IF NOT EXISTS - not supported in PostgreSQL for constraints)
-- Run this script only once or handle errors manually if constraints already exist

ALTER TABLE dtm_ads.ad_campaigns
ADD CONSTRAINT fk_ad_campaigns_manager_id
FOREIGN KEY (manager_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE;

ALTER TABLE dtm_ads.ad_campaigns
ADD CONSTRAINT fk_ad_campaigns_advertiser_id
FOREIGN KEY (advertiser_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE;

ALTER TABLE dtm_ads.ads
ADD CONSTRAINT fk_ads_campaign_id
FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE CASCADE;

ALTER TABLE dtm_ads.ads
ADD CONSTRAINT fk_ads_slot_id
FOREIGN KEY (slot_id) REFERENCES dtm_ads.ad_slots(id) ON DELETE CASCADE;

ALTER TABLE dtm_ads.ad_impressions
ADD CONSTRAINT fk_ad_impressions_ad_id
FOREIGN KEY (ad_id) REFERENCES dtm_ads.ads(id) ON DELETE CASCADE;

ALTER TABLE dtm_ads.ad_impressions
ADD CONSTRAINT fk_ad_impressions_user_id
FOREIGN KEY (user_id) REFERENCES dtm_base.users(id) ON DELETE SET NULL;

ALTER TABLE dtm_ads.ad_clicks
ADD CONSTRAINT fk_ad_clicks_ad_id
FOREIGN KEY (ad_id) REFERENCES dtm_ads.ads(id) ON DELETE CASCADE;

ALTER TABLE dtm_ads.ad_clicks
ADD CONSTRAINT fk_ad_clicks_user_id
FOREIGN KEY (user_id) REFERENCES dtm_base.users(id) ON DELETE SET NULL;

-- Add check constraints for data integrity
ALTER TABLE dtm_ads.ad_campaigns
ADD CONSTRAINT chk_ad_campaigns_dates
CHECK (end_date > start_date);

ALTER TABLE dtm_ads.ad_campaigns
ADD CONSTRAINT chk_ad_campaigns_status
CHECK (status IN ('pending', 'active', 'paused', 'rejected', 'completed'));

ALTER TABLE dtm_ads.ads
ADD CONSTRAINT chk_ads_status
CHECK (status IN ('pending', 'active', 'paused', 'rejected'));

ALTER TABLE dtm_ads.ads
ADD CONSTRAINT chk_ads_weight
CHECK (weight > 0 AND weight <= 1000);

ALTER TABLE dtm_ads.ad_slots
ADD CONSTRAINT chk_ad_slots_dimensions
CHECK (width > 0 AND height > 0);

ALTER TABLE dtm_ads.ad_slots
ADD CONSTRAINT chk_ad_slots_page
CHECK (page IN ('all', 'home', 'subnets', 'companies', 'newsletter'));

-- Add triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables that have updated_at columns
DROP TRIGGER IF EXISTS update_ad_campaigns_updated_at ON dtm_ads.ad_campaigns;
CREATE TRIGGER update_ad_campaigns_updated_at 
    BEFORE UPDATE ON dtm_ads.ad_campaigns 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_ads_updated_at ON dtm_ads.ads;
CREATE TRIGGER update_ads_updated_at 
    BEFORE UPDATE ON dtm_ads.ads 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
