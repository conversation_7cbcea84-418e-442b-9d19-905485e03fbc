// src/presentation/controllers/adminController.js
const AdminService = require("../../application/services/AdminService");
const AdsService = require("../../application/services/AdsService");
const RateLimitService = require("../../application/services/RateLimitService");
const ReportingService = require("../../application/services/ReportingService");
const FraudDetectionService = require("../../application/services/FraudDetectionService");
const NetworkUpdateService = require("../../application/services/NetworkUpdateService");
const { getScheduler } = require("../../infrastructure/scheduler");
const db = require("../../infrastructure/database/knex");
const {
	sendSuccess,
	sendError,
	sendNotFound,
	sendUnauthorized,
	sendForbidden,
} = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

// ─── AD<PERSON>NAGEMENT ────────────────────────────────────────────────

const getPendingCampaigns = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware and available in req.adminUser

	const { limit = 50, offset = 0 } = req.query;

	const adminService = new AdminService();
	const campaigns = await adminService.getPendingCampaigns(parseInt(limit), parseInt(offset));

	return sendSuccess(res, campaigns, "Pending campaigns retrieved successfully");
});

const getApprovedCampaigns = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware and available in req.adminUser

	const { limit = 50, offset = 0 } = req.query;

	const adminService = new AdminService();
	const campaigns = await adminService.getApprovedCampaigns(parseInt(limit), parseInt(offset));

	return sendSuccess(res, campaigns, "Approved campaigns retrieved successfully");
});

const getRejectedCampaigns = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware and available in req.adminUser

	const { limit = 50, offset = 0 } = req.query;

	const adminService = new AdminService();
	const campaigns = await adminService.getRejectedCampaigns(parseInt(limit), parseInt(offset));

	return sendSuccess(res, campaigns, "Rejected campaigns retrieved successfully");
});

const getPendingAds = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const { limit = 50, offset = 0 } = req.query;

	const adminService = new AdminService();
	const ads = await adminService.getPendingAds(parseInt(limit), parseInt(offset));

	return sendSuccess(res, ads, "Pending ads retrieved successfully");
});

const approveCampaign = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const user = req.adminUser;
	const { id: campaignId } = req.params;
	const { notes } = req.body;

	const adminService = new AdminService();
	const updatedCampaign = await adminService.approveCampaign(campaignId, user.id, notes);

	return sendSuccess(res, updatedCampaign, "Campaign approved successfully");
});

const rejectCampaign = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	// Admin user is already verified by middleware

	const { id: campaignId } = req.params;
	const { reason, notes } = req.body;

	if (!reason) {
		return sendError(res, "Rejection reason is required", null, 400);
	}

	const adminService = new AdminService();
	const updatedCampaign = await adminService.rejectCampaign(campaignId, req.adminUser.id, reason, notes);

	return sendSuccess(res, updatedCampaign, "Campaign rejected successfully");
});

const approveAd = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const user = req.adminUser;
	const { id: adId } = req.params;
	const { notes } = req.body;

	const adminService = new AdminService();
	const updatedAd = await adminService.approveAd(adId, user.id, notes);

	return sendSuccess(res, updatedAd, "Ad approved successfully");
});

const rejectAd = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const user = req.adminUser;
	const { id: adId } = req.params;
	const { reason, notes } = req.body;

	if (!reason) {
		return sendError(res, "Rejection reason is required", null, 400);
	}

	const adminService = new AdminService();
	const updatedAd = await adminService.rejectAd(adId, user.id, reason, notes);

	return sendSuccess(res, updatedAd, "Ad rejected successfully");
});

const getRejectionReasons = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const { entity_type } = req.query;

	if (!entity_type || !["campaign", "ad"].includes(entity_type)) {
		return sendError(res, "Valid entity_type (campaign or ad) is required", null, 400);
	}

	const adminService = new AdminService();
	const reasons = await adminService.getRejectionReasons(entity_type);

	return sendSuccess(res, reasons, "Rejection reasons retrieved successfully");
});

// ─── ADMIN VERIFICATION ───────────────────────────────────────────────────────

const verifyAdmin = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware and available in req.adminUser
	return sendSuccess(res, { isAdmin: true, user: req.adminUser }, "Admin access verified");
});

// ─── ADMIN NOTIFICATIONS ──────────────────────────────────────────────────────

const getAdminNotifications = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware and available in req.adminUser
	const user = req.adminUser;

	const { limit = 20, offset = 0 } = req.query;

	const notifications = await db("dtm_ads.ad_notifications")
		.where({ user_id: user.id })
		.orderBy("created_at", "desc")
		.limit(parseInt(limit))
		.offset(parseInt(offset));

	return sendSuccess(res, notifications, "Admin notifications retrieved successfully");
});

const markNotificationRead = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const user = req.adminUser;
	const { id: notificationId } = req.params;

	const [updatedNotification] = await db("dtm_ads.ad_notifications")
		.where({ id: notificationId, user_id: user.id })
		.update({ is_read: true, updated_at: new Date() })
		.returning("*");

	if (!updatedNotification) {
		return sendNotFound(res, "Notification not found");
	}

	return sendSuccess(res, updatedNotification, "Notification marked as read");
});

// ─── RATE LIMIT MANAGEMENT ────────────────────────────────────────────────────

const getRateLimitStats = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware

	const { limit_type } = req.query;

	const rateLimitService = new RateLimitService();

	if (limit_type) {
		const stats = await rateLimitService.getRateLimitStats(limit_type);
		return sendSuccess(res, { [limit_type]: stats }, "Rate limit stats retrieved successfully");
	} else {
		// Get stats for all limit types
		const limitTypes = [
			"api:general",
			"api:auth",
			"api:upload",
			"api:analytics",
			"user:general",
			"user:campaigns",
			"user:ads",
			"admin:general",
			"admin:approval",
			"ip:general",
			"ip:strict",
		];

		const allStats = {};
		for (const type of limitTypes) {
			allStats[type] = await rateLimitService.getRateLimitStats(type);
		}

		return sendSuccess(res, allStats, "All rate limit stats retrieved successfully");
	}
});

const setCustomRateLimit = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	// Admin user is already verified by middleware

	const { identifier, limit_type, requests, window } = req.body;

	if (!identifier || !limit_type || !requests || !window) {
		return sendError(res, "identifier, limit_type, requests, and window are required", null, 400);
	}

	if (requests <= 0 || window <= 0) {
		return sendError(res, "requests and window must be positive numbers", null, 400);
	}

	const rateLimitService = new RateLimitService();
	const success = await rateLimitService.setCustomRateLimit(identifier, limit_type, {
		requests: parseInt(requests),
		window: parseInt(window),
	});

	if (success) {
		return sendSuccess(res, { identifier, limit_type, requests, window }, "Custom rate limit set successfully");
	} else {
		return sendError(res, "Failed to set custom rate limit", null, 500);
	}
});

const resetRateLimit = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	// Admin user is already verified by middleware

	const { identifier, limit_type } = req.body;

	if (!identifier || !limit_type) {
		return sendError(res, "identifier and limit_type are required", null, 400);
	}

	const rateLimitService = new RateLimitService();
	const success = await rateLimitService.resetRateLimit(identifier, limit_type);

	if (success) {
		return sendSuccess(res, { identifier, limit_type }, "Rate limit reset successfully");
	} else {
		return sendError(res, "Failed to reset rate limit", null, 500);
	}
});

const getRateLimitStatus = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const { identifier, limit_type } = req.query;

	if (!identifier || !limit_type) {
		return sendError(res, "identifier and limit_type query parameters are required", null, 400);
	}

	const rateLimitService = new RateLimitService();
	const status = await rateLimitService.getRateLimitStatus(identifier, limit_type);

	return sendSuccess(res, status, "Rate limit status retrieved successfully");
});

// ─── REPORTING & ANALYTICS ────────────────────────────────────────────────────

const getAdminAnalytics = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware

	const { range = "last30days" } = req.query;

	// Calculate date range
	let startDate, endDate;
	const today = new Date();
	endDate = today.toISOString().split("T")[0];

	switch (range) {
		case "last7days":
			startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0];
			break;
		case "last30days":
			startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0];
			break;
		case "last90days":
			startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split("T")[0];
			break;
		default:
			startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0];
	}

	try {
		// Get all campaign analytics for admin view
		const analyticsService = new AnalyticsService();

		// Get total metrics across all campaigns
		const totalImpressions = await db("dtm_ads.ad_impressions")
			.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"])
			.count("* as count")
			.first();

		const totalClicks = await db("dtm_ads.ad_clicks")
			.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"])
			.count("* as count")
			.first();

		const totalSpend = await db("dtm_ads.ad_campaigns")
			.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"])
			.sum("budget as total")
			.first();

		const impressions = parseInt(totalImpressions.count) || 0;
		const clicks = parseInt(totalClicks.count) || 0;
		const spend = parseFloat(totalSpend.total) || 0;
		const ctr = impressions > 0 ? clicks / impressions : 0;

		// Get daily metrics for the admin dashboard
		const dailyMetrics = await analyticsService.getDailyMetrics(
			await db("dtm_ads.ads").pluck("id"), // All ad IDs
			startDate,
			endDate
		);

		const analytics = {
			total_impressions: impressions,
			total_clicks: clicks,
			total_spend: spend,
			ctr: parseFloat(ctr.toFixed(4)),
			daily_metrics: dailyMetrics,
		};

		return sendSuccess(res, analytics, "Admin analytics retrieved successfully");
	} catch (error) {
		logger.error("Error getting admin analytics", { error, range, startDate, endDate });
		return sendError(res, "Failed to retrieve admin analytics");
	}
});

const generateDailyReports = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	// Admin user is already verified by middleware

	const { date } = req.body;

	const reportingService = new ReportingService();
	const results = await reportingService.generateDailyReports(date);

	return sendSuccess(res, results, "Daily reports generated successfully");
});

const generateWeeklyReports = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	// Admin user is already verified by middleware

	const { week_start } = req.query;

	const reportingService = new ReportingService();
	const results = await reportingService.generateWeeklyReports(week_start);

	return sendSuccess(res, results, "Weekly reports generated successfully");
});

const generateMonthlyReports = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const { month } = req.query;

	const reportingService = new ReportingService();
	const results = await reportingService.generateMonthlyReports(month);

	return sendSuccess(res, results, "Monthly reports generated successfully");
});

const getCampaignTrends = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	// Admin user is already verified by middleware

	const { id: campaignId } = req.params;
	const { days = 30 } = req.query;

	const reportingService = new ReportingService();
	const trends = await reportingService.getCampaignTrends(campaignId, parseInt(days));

	return sendSuccess(res, trends, "Campaign trends retrieved successfully");
});

const cleanupOldReports = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const { retention_days = 365 } = req.body;

	const reportingService = new ReportingService();
	const results = await reportingService.cleanupOldReports(parseInt(retention_days));

	return sendSuccess(res, results, "Old reports cleaned up successfully");
});

// ─── FRAUD DETECTION ──────────────────────────────────────────────────────────

const getFraudStatistics = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	// Admin user is already verified by middleware

	const { days = 7, ad_id } = req.query;

	const fraudDetectionService = new FraudDetectionService();
	const stats = await fraudDetectionService.getFraudStatistics({
		days: parseInt(days),
		ad_id: ad_id ? parseInt(ad_id) : null,
	});

	return sendSuccess(res, stats, "Fraud statistics retrieved successfully");
});

const getFraudAnalyses = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware

	const { limit = 50, offset = 0, risk_level, action_recommended, ad_id, days = 7 } = req.query;

	const startDate = new Date(Date.now() - parseInt(days) * 24 * 60 * 60 * 1000);

	let query = db("dtm_ads.fraud_analysis")
		.leftJoin("dtm_ads.ads", "fraud_analysis.ad_id", "ads.id")
		.leftJoin("dtm_ads.ad_campaigns", "ads.campaign_id", "ad_campaigns.id")
		.where("fraud_analysis.created_at", ">=", startDate)
		.select("fraud_analysis.*", "ads.title as ad_title", "ad_campaigns.name as campaign_name");

	if (risk_level) {
		query = query.where("fraud_analysis.risk_level", risk_level);
	}

	if (action_recommended) {
		query = query.where("fraud_analysis.action_recommended", action_recommended);
	}

	if (ad_id) {
		query = query.where("fraud_analysis.ad_id", parseInt(ad_id));
	}

	const analyses = await query
		.orderBy("fraud_analysis.created_at", "desc")
		.limit(parseInt(limit))
		.offset(parseInt(offset));

	// Parse JSON fields
	const processedAnalyses = analyses.map((analysis) => ({
		...analysis,
		suspicious_factors: analysis.suspicious_factors ? JSON.parse(analysis.suspicious_factors) : [],
	}));

	return sendSuccess(res, processedAnalyses, "Fraud analyses retrieved successfully");
});

const analyzeClickForFraud = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const { click_id } = req.params;

	// Get click data
	const clickData = await db("dtm_ads.ad_clicks").where("id", click_id).first();

	if (!clickData) {
		return sendNotFound(res, "Click not found");
	}

	const fraudDetectionService = new FraudDetectionService();
	const analysis = await fraudDetectionService.analyzeClick(clickData);

	return sendSuccess(res, analysis, "Click fraud analysis completed");
});

const analyzeImpressionForFraud = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const { impression_id } = req.params;

	// Get impression data
	const impressionData = await db("dtm_ads.ad_impressions").where("id", impression_id).first();

	if (!impressionData) {
		return sendNotFound(res, "Impression not found");
	}

	const fraudDetectionService = new FraudDetectionService();
	const analysis = await fraudDetectionService.analyzeImpression(impressionData);

	return sendSuccess(res, analysis, "Impression fraud analysis completed");
});

// ─── SCHEDULER & NETWORK UPDATES ──────────────────────────────────────────────

const getSchedulerStatus = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const scheduler = getScheduler();

	if (!scheduler) {
		return sendError(res, "Scheduler not initialized", null, 500);
	}

	const status = {
		initialized: true,
		networkUpdatesEnabled: process.env.NETWORK_UPDATES_ENABLED === "true",
		jobs: scheduler.getJobStatuses(),
		timestamp: new Date().toISOString(),
	};

	return sendSuccess(res, status, "Scheduler status retrieved successfully");
});

const triggerNetworkUpdate = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware

	const { type = "all" } = req.body;

	const networkUpdateService = new NetworkUpdateService();
	let result;

	switch (type) {
		case "tao":
			result = await networkUpdateService.updateAllTao();
			break;
		case "github":
			result = await networkUpdateService.updateGithubData();
			break;
		case "validators":
			result = await networkUpdateService.updateValidatorData();
			break;
		case "stats":
			result = await networkUpdateService.updateNetworkStats();
			break;
		case "all":
		default:
			result = await networkUpdateService.updateAllNetworkData();
			break;
	}

	return sendSuccess(res, result, `Network update (${type}) completed`);
});

const getNetworkUpdateStatus = asyncHandler(async (req, res) => {
	// Admin user is already verified by middleware
	const networkUpdateService = new NetworkUpdateService();
	const status = await networkUpdateService.getUpdateStatus();

	return sendSuccess(res, status, "Network update status retrieved successfully");
});

const stopSchedulerJob = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	// Admin user is already verified by middleware

	const { job_name } = req.params;

	const scheduler = getScheduler();

	if (!scheduler) {
		return sendError(res, "Scheduler not initialized", null, 500);
	}

	const success = scheduler.stopJob(job_name);

	if (success) {
		return sendSuccess(res, { job_name, stopped: true }, "Job stopped successfully");
	} else {
		return sendNotFound(res, "Job not found");
	}
});

const restartSchedulerJob = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	// Admin user is already verified by middleware

	const { job_name } = req.params;
	const { schedule } = req.body;

	if (!schedule) {
		return sendError(res, "Schedule is required", null, 400);
	}

	const scheduler = getScheduler();

	if (!scheduler) {
		return sendError(res, "Scheduler not initialized", null, 500);
	}

	// Stop existing job if it exists
	scheduler.stopJob(job_name);

	// Define job tasks
	const jobTasks = {
		"tao-update": () => scheduler.runTaoUpdate(),
		"github-update": () => scheduler.runGithubUpdate(),
		"network-comprehensive-update": () => scheduler.runComprehensiveNetworkUpdate(),
		"daily-reports": () => scheduler.runDailyReports(),
		"weekly-reports": () => scheduler.runWeeklyReports(),
		"monthly-reports": () => scheduler.runMonthlyReports(),
		"cleanup-reports": () => scheduler.runReportCleanup(),
		"cleanup-notifications": () => scheduler.runNotificationCleanup(),
		"check-low-balances": () => scheduler.checkLowBalances(),
		"health-check": () => scheduler.runHealthCheck(),
	};

	const task = jobTasks[job_name];
	if (!task) {
		return sendError(res, "Unknown job name", null, 400);
	}

	// Restart the job with new schedule
	scheduler.scheduleJob(job_name, schedule, task);

	return sendSuccess(
		res,
		{
			job_name,
			schedule,
			restarted: true,
		},
		"Job restarted successfully"
	);
});

/**
 * Log critical error for manual intervention
 */
const logCriticalError = asyncHandler(async (req, res) => {
	const { type, payment_intent_id, campaign_id, error_message, timestamp } = req.body;

	if (!type || !error_message) {
		return sendError(res, "Missing required fields", null, 400);
	}

	try {
		// Create critical_errors table if it doesn't exist
		const hasTable = await db.schema.hasTable("dtm_ads.critical_errors");
		if (!hasTable) {
			await db.schema.createTable("dtm_ads.critical_errors", (table) => {
				table.increments("id").primary();
				table.string("error_type").notNullable();
				table.string("payment_intent_id").nullable();
				table.integer("campaign_id").nullable();
				table.text("error_message").notNullable();
				table.timestamp("timestamp").defaultTo(db.fn.now());
				table.string("status").defaultTo("pending");
				table.timestamp("created_at").defaultTo(db.fn.now());
				table.timestamp("resolved_at").nullable();
			});
		}

		// Log to database for admin review
		await db("dtm_ads.critical_errors").insert({
			error_type: type,
			payment_intent_id: payment_intent_id || null,
			campaign_id: campaign_id || null,
			error_message,
			timestamp: timestamp || new Date(),
			status: "pending",
			created_at: new Date(),
		});

		logger.error("Critical error logged", {
			type,
			payment_intent_id,
			campaign_id,
			error_message,
		});

		return sendSuccess(res, { logged: true }, "Critical error logged successfully");
	} catch (error) {
		logger.error("Error logging critical error", { error, type, payment_intent_id, campaign_id });
		return sendError(res, "Failed to log critical error", error.message, 500);
	}
});

module.exports = {
	// Campaign Management
	getPendingCampaigns,
	getApprovedCampaigns,
	getRejectedCampaigns,
	approveCampaign,
	rejectCampaign,

	// Ad Management
	getPendingAds,
	approveAd,
	rejectAd,

	// Utilities
	getRejectionReasons,

	// Admin Verification
	verifyAdmin,

	// Notifications
	getAdminNotifications,
	markNotificationRead,

	// Rate Limit Management
	getRateLimitStats,
	setCustomRateLimit,
	resetRateLimit,
	getRateLimitStatus,

	// Reporting & Analytics
	getAdminAnalytics,
	generateDailyReports,
	generateWeeklyReports,
	generateMonthlyReports,
	getCampaignTrends,
	cleanupOldReports,

	// Fraud Detection
	getFraudStatistics,
	getFraudAnalyses,
	analyzeClickForFraud,
	analyzeImpressionForFraud,

	// Scheduler & Network Updates
	getSchedulerStatus,
	triggerNetworkUpdate,
	getNetworkUpdateStatus,
	stopSchedulerJob,
	restartSchedulerJob,

	// Error management
	logCriticalError,
};
