// src/presentation/routes/adminRoutes.js
const express = require("express");
const router = express.Router();
const checkJwt = require("../../middleware/auth");
const checkAdminAuth = require("../../middleware/adminAuth");
const checkInternalKey = require("../../middleware/internal");
const rateLimits = require("../../middleware/rateLimitMiddleware");
const adminController = require("../controllers/adminController");

// Admin verification endpoint
router.get("/verify", checkJwt, checkAdminAuth, rateLimits.adminGeneral, adminController.verifyAdmin);

// Campaign management
router.get(
	"/campaigns/pending",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminGeneral,
	adminController.getPendingCampaigns
);
router.get(
	"/campaigns/approved",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminGeneral,
	adminController.getApprovedCampaigns
);
router.get(
	"/campaigns/rejected",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminGeneral,
	adminController.getRejectedCampaigns
);
router.post(
	"/campaigns/:id/approve",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminApproval,
	adminController.approveCampaign
);
router.post(
	"/campaigns/:id/reject",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminApproval,
	adminController.rejectCampaign
);

// Ad management
router.get("/ads/pending", checkJwt, checkAdminAuth, rateLimits.adminGeneral, adminController.getPendingAds);
router.post("/ads/:id/approve", checkJwt, checkAdminAuth, rateLimits.adminApproval, adminController.approveAd);
router.post("/ads/:id/reject", checkJwt, checkAdminAuth, rateLimits.adminApproval, adminController.rejectAd);

// Utilities
router.get("/rejection-reasons", checkJwt, checkAdminAuth, adminController.getRejectionReasons);

// Notifications
router.get("/notifications", checkJwt, checkAdminAuth, adminController.getAdminNotifications);
router.put("/notifications/:id/read", checkJwt, checkAdminAuth, adminController.markNotificationRead);

// Rate limit management
router.get("/rate-limits/stats", checkJwt, checkAdminAuth, adminController.getRateLimitStats);
router.post("/rate-limits/custom", checkJwt, checkAdminAuth, adminController.setCustomRateLimit);
router.post("/rate-limits/reset", checkJwt, checkAdminAuth, adminController.resetRateLimit);
router.get("/rate-limits/status", checkJwt, checkAdminAuth, adminController.getRateLimitStatus);

// Reporting & Analytics
router.get("/analytics", checkJwt, checkAdminAuth, rateLimits.adminGeneral, adminController.getAdminAnalytics);
router.post("/reports/daily", checkJwt, checkAdminAuth, rateLimits.adminGeneral, adminController.generateDailyReports);
router.get("/reports/weekly", checkJwt, checkAdminAuth, rateLimits.adminGeneral, adminController.generateWeeklyReports);
router.get(
	"/reports/monthly",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminGeneral,
	adminController.generateMonthlyReports
);
router.get(
	"/campaigns/:id/trends",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminGeneral,
	adminController.getCampaignTrends
);
router.post("/reports/cleanup", checkJwt, checkAdminAuth, rateLimits.adminGeneral, adminController.cleanupOldReports);

// Fraud Detection
router.get("/fraud/statistics", checkJwt, checkAdminAuth, rateLimits.adminGeneral, adminController.getFraudStatistics);
router.get("/fraud/analyses", checkJwt, checkAdminAuth, rateLimits.adminGeneral, adminController.getFraudAnalyses);
router.post(
	"/fraud/analyze/click/:click_id",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminGeneral,
	adminController.analyzeClickForFraud
);
router.post(
	"/fraud/analyze/impression/:impression_id",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminGeneral,
	adminController.analyzeImpressionForFraud
);

// Scheduler & Network Updates
router.get("/scheduler/status", checkJwt, checkAdminAuth, rateLimits.adminGeneral, adminController.getSchedulerStatus);
router.post("/network/update", checkJwt, checkAdminAuth, rateLimits.adminGeneral, adminController.triggerNetworkUpdate);
router.get(
	"/network/status",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminGeneral,
	adminController.getNetworkUpdateStatus
);
router.post(
	"/scheduler/jobs/:job_name/stop",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminGeneral,
	adminController.stopSchedulerJob
);
router.post(
	"/scheduler/jobs/:job_name/restart",
	checkJwt,
	checkAdminAuth,
	rateLimits.adminGeneral,
	adminController.restartSchedulerJob
);

// Error management
router.post("/log-critical-error", checkInternalKey, rateLimits.ipStrict, adminController.logCriticalError);

module.exports = router;
