// app/dashboard/ads/[id]/edit/page.tsx
"use client";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Save, Trash2 } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Ad {
	id: number;
	campaign_id: number;
	slot_id: number;
	title: string;
	image_url: string;
	target_url: string;
	max_impressions?: number | null;
	max_clicks?: number | null;
	weight: number;
	status: "pending" | "active" | "paused" | "rejected";
	rejection_reason?: string | null;
}

export default function EditAdPage() {
	const router = useRouter();
	const params = useParams();
	const { toast } = useToast();
	const adId = params.id as string;

	const [ad, setAd] = useState<Ad | null>(null);
	const [loading, setLoading] = useState(true);
	const [saving, setSaving] = useState(false);
	const [deleting, setDeleting] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const [formData, setFormData] = useState({
		title: "",
		image_url: "",
		target_url: "",
		max_impressions: "",
		max_clicks: "",
		weight: "1",
		status: "pending" as Ad["status"],
	});

	useEffect(() => {
		if (adId) {
			fetchAd();
		}
	}, [adId]);

	const fetchAd = async () => {
		try {
			setLoading(true);

			const response = await fetch(`/api/user/ads/${adId}`);

			if (!response.ok) {
				throw new Error(`Failed to fetch ad: ${response.status}`);
			}

			const result = await response.json();
			if (result.success) {
				const adData = result.data;
				setAd(adData);

				// Populate form with existing data
				setFormData({
					title: adData.title || "",
					image_url: adData.image_url || "",
					target_url: adData.target_url || "",
					max_impressions: adData.max_impressions?.toString() || "",
					max_clicks: adData.max_clicks?.toString() || "",
					weight: adData.weight?.toString() || "1",
					status: adData.status || "pending",
				});
			} else {
				throw new Error(result.message || "Failed to fetch ad");
			}
		} catch (error) {
			console.error("Error fetching ad:", error);
			setError(error instanceof Error ? error.message : "Failed to fetch ad");
		} finally {
			setLoading(false);
		}
	};

	const handleInputChange = (field: string, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
	};

	const handleSave = async () => {
		try {
			setSaving(true);

			// Prepare update data
			const updateData = {
				title: formData.title,
				image_url: formData.image_url,
				target_url: formData.target_url,
				max_impressions: formData.max_impressions ? parseInt(formData.max_impressions) : null,
				max_clicks: formData.max_clicks ? parseInt(formData.max_clicks) : null,
				weight: parseInt(formData.weight),
			};

			const response = await fetch(`/api/user/ads/${adId}`, {
				method: "PUT",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(updateData),
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Failed to update ad: ${errorText}`);
			}

			const result = await response.json();
			if (result.success) {
				toast({
					title: "Ad Updated",
					description: "Your ad has been successfully updated.",
				});
				router.push(`/dashboard/campaigns/${ad?.campaign_id}`);
			} else {
				throw new Error(result.message || "Failed to update ad");
			}
		} catch (error) {
			console.error("Error updating ad:", error);
			toast({
				title: "Update Failed",
				description: error instanceof Error ? error.message : "Failed to update ad",
				variant: "destructive",
			});
		} finally {
			setSaving(false);
		}
	};

	const handleDelete = async () => {
		if (!confirm("Are you sure you want to delete this ad? This action cannot be undone.")) {
			return;
		}

		try {
			setDeleting(true);

			const response = await fetch(`/api/user/ads/${adId}`, {
				method: "DELETE",
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Failed to delete ad: ${errorText}`);
			}

			const result = await response.json();
			if (result.success) {
				toast({
					title: "Ad Deleted",
					description: "Your ad has been successfully deleted.",
				});
				router.push(`/dashboard/campaigns/${ad?.campaign_id}`);
			} else {
				throw new Error(result.message || "Failed to delete ad");
			}
		} catch (error) {
			console.error("Error deleting ad:", error);
			toast({
				title: "Delete Failed",
				description: error instanceof Error ? error.message : "Failed to delete ad",
				variant: "destructive",
			});
		} finally {
			setDeleting(false);
		}
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
					<p className="text-sm text-muted-foreground">Loading ad...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<p className="text-red-600 mb-4">{error}</p>
					<Button onClick={() => router.back()}>Go Back</Button>
				</div>
			</div>
		);
	}

	if (!ad) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<p className="text-gray-600 mb-4">Ad not found</p>
					<Button onClick={() => router.back()}>Go Back</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50 py-8">
			<div className="max-w-4xl mx-auto px-4">
				<div className="mb-6">
					<Button variant="ghost" onClick={() => router.back()} className="mb-4">
						<ArrowLeft className="mr-2 h-4 w-4" />
						Back to Campaign
					</Button>
					<div className="flex items-center justify-between">
						<div>
							<h1 className="text-3xl font-bold text-gray-900">Edit Ad</h1>
							<p className="text-gray-600">Update your ad details</p>
						</div>
					</div>
				</div>

				{/* Edit Form */}
				<Card>
					<CardHeader>
						<CardTitle>Ad Details</CardTitle>
						<CardDescription>
							Update your ad information. Changes will be reviewed if the ad is active.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-6">
						{/* Basic Information */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="title">Ad Title</Label>
								<Input
									id="title"
									value={formData.title}
									onChange={(e) => handleInputChange("title", e.target.value)}
									placeholder="Enter ad title"
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="status">Status</Label>
								<Select
									value={formData.status}
									onValueChange={(value) => handleInputChange("status", value)}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="pending">Pending</SelectItem>
										<SelectItem value="active">Active</SelectItem>
										<SelectItem value="paused">Paused</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>

						{/* Creative Information */}
						<div className="space-y-4">
							<div className="space-y-2">
								<Label htmlFor="image_url">Image URL</Label>
								<Input
									id="image_url"
									type="url"
									value={formData.image_url}
									onChange={(e) => handleInputChange("image_url", e.target.value)}
									placeholder="https://example.com/image.jpg"
								/>
								{/* Image Preview */}
								{formData.image_url && (
									<div className="mt-3">
										<Label className="text-sm font-medium">Preview:</Label>
										<div className="mt-2 relative w-full max-w-md border rounded-lg overflow-hidden bg-gray-50">
											<img
												src={formData.image_url}
												alt="Ad preview"
												className="w-full h-auto object-cover"
												onError={(e) => {
													const target = e.target as HTMLImageElement;
													target.style.display = "none";
													const errorDiv = target.nextElementSibling as HTMLElement;
													if (errorDiv) errorDiv.style.display = "block";
												}}
												onLoad={(e) => {
													const target = e.target as HTMLImageElement;
													target.style.display = "block";
													const errorDiv = target.nextElementSibling as HTMLElement;
													if (errorDiv) errorDiv.style.display = "none";
												}}
											/>
											<div
												className="hidden p-4 text-center text-sm text-red-600"
												style={{ display: "none" }}
											>
												Failed to load image. Please check the URL.
											</div>
										</div>
									</div>
								)}
							</div>
							<div className="space-y-2">
								<Label htmlFor="target_url">Target URL</Label>
								<Input
									id="target_url"
									type="url"
									value={formData.target_url}
									onChange={(e) => handleInputChange("target_url", e.target.value)}
									placeholder="https://example.com"
								/>
							</div>
						</div>

						{/* Performance Settings */}
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div className="space-y-2">
								<Label htmlFor="max_impressions">Max Impressions</Label>
								<Input
									id="max_impressions"
									type="number"
									value={formData.max_impressions}
									onChange={(e) => handleInputChange("max_impressions", e.target.value)}
									placeholder="Unlimited"
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="max_clicks">Max Clicks</Label>
								<Input
									id="max_clicks"
									type="number"
									value={formData.max_clicks}
									onChange={(e) => handleInputChange("max_clicks", e.target.value)}
									placeholder="Unlimited"
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="weight">Weight</Label>
								<Input
									id="weight"
									type="number"
									min="1"
									max="1000"
									value={formData.weight}
									onChange={(e) => handleInputChange("weight", e.target.value)}
								/>
							</div>
						</div>

						{ad.rejection_reason && (
							<div className="p-4 bg-red-50 border border-red-200 rounded-lg">
								<h4 className="text-sm font-medium text-red-800 mb-2">Rejection Reason</h4>
								<p className="text-sm text-red-700">{ad.rejection_reason}</p>
							</div>
						)}

						{/* Action Buttons */}
						<div className="flex gap-4 pt-6">
							<Button onClick={handleSave} disabled={saving} className="flex-1">
								<Save className="mr-2 h-4 w-4" />
								{saving ? "Saving..." : "Save Changes"}
							</Button>
							<Button variant="destructive" onClick={handleDelete} disabled={deleting}>
								<Trash2 className="mr-2 h-4 w-4" />
								{deleting ? "Deleting..." : "Delete Ad"}
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
