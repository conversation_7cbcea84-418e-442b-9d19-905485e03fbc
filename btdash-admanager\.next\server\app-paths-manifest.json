{"/api/user/analytics/route": "app/api/user/analytics/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/admin/campaigns/pending/route": "app/api/admin/campaigns/pending/route.js", "/api/user/campaigns/route": "app/api/user/campaigns/route.js", "/api/user/company/route": "app/api/user/company/route.js", "/api/user/campaigns/[id]/route": "app/api/user/campaigns/[id]/route.js", "/page": "app/page.js", "/dashboard/page": "app/dashboard/page.js", "/admin/requests/[id]/page": "app/admin/requests/[id]/page.js", "/admin/page": "app/admin/page.js"}