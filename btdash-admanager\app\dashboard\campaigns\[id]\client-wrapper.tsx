// app/dashboard/campaigns/[id]/client-wrapper.tsx
"use client";

import { countries } from "@/components/country-selector";
import { PageTargetingDisplay } from "@/components/page-targeting-display";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import type { Ad, AdCampaign } from "@/lib/db/models";
import { cn } from "@/lib/utils";
import { ArrowLeft, BarChart3, Edit, FileText, Globe } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface CampaignDetailsClientWrapperProps {
	campaign: AdCampaign & {
		placement?: {
			id: number;
			name: string;
			page: string;
			width: number;
			height: number;
		};
		impressions?: number;
		clicks?: number;
		ctr?: number;
	};
	ads: Ad[];
}

export default function CampaignDetailsClientWrapper({ campaign, ads }: CampaignDetailsClientWrapperProps) {
	const router = useRouter();
	const { toast } = require("@/hooks/use-toast").useToast();
	const [deletingAdId, setDeletingAdId] = useState<number | null>(null);

	if (!campaign) {
		return <div className="p-6 text-center">Campaign not found</div>;
	}

	// Get the primary ad (you might want to enhance this logic)
	const primaryAd = ads[0];

	const handleEditAd = (adId: number) => {
		router.push(`/dashboard/ads/${adId}/edit`);
	};

	const handleCampaignTargeting = () => {
		router.push(`/dashboard/campaigns/${campaign.id}/targeting`);
	};

	const handleAdAnalytics = (adId: number) => {
		router.push(`/dashboard/ads/${adId}/analytics`);
	};

	const handleDeleteAd = async (adId: number) => {
		if (!confirm("Are you sure you want to delete this ad? This action cannot be undone.")) {
			return;
		}

		try {
			setDeletingAdId(adId);

			const response = await fetch(`/api/user/ads/${adId}`, {
				method: "DELETE",
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Failed to delete ad: ${errorText}`);
			}

			const result = await response.json();
			if (result.success) {
				toast({
					title: "Ad Deleted",
					description: "The ad has been successfully deleted.",
				});
				// Refresh the page to update the ads list
				window.location.reload();
			} else {
				throw new Error(result.message || "Failed to delete ad");
			}
		} catch (error) {
			console.error("Error deleting ad:", error);
			toast({
				title: "Delete Failed",
				description: error instanceof Error ? error.message : "Failed to delete ad",
				variant: "destructive",
			});
		} finally {
			setDeletingAdId(null);
		}
	};

	const handleAddAd = () => {
		router.push(`/dashboard/campaigns/${campaign.id}/ads/create`);
	};

	// Helper function to get country names from codes
	const getCountryNames = (countryCodes: string[] = []) => {
		return countryCodes
			.map((code) => {
				const country = countries.find((c) => c.value === code);
				return country ? country.label : code;
			})
			.join(", ");
	};

	// Get targeting information
	const targeting = campaign.targeting || { countries: { mode: "all" } };
	const { mode, include, exclude } = targeting.countries;
	let targetingDescription = "Showing to all countries";

	if (mode === "include" && include?.length) {
		targetingDescription = `Showing only in: ${getCountryNames(include)}`;
	} else if (mode === "exclude" && exclude?.length) {
		targetingDescription = `Showing everywhere except: ${getCountryNames(exclude)}`;
	}

	return (
		<div className="flex flex-col gap-6">
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-2">
					<Button variant="ghost" size="icon" onClick={() => router.back()}>
						<ArrowLeft className="h-4 w-4" />
					</Button>
					<h1 className="text-3xl font-bold tracking-tight">{campaign.name}</h1>
				</div>
				<div className="flex gap-2">
					<Button variant="outline" onClick={handleCampaignTargeting}>
						<Globe className="mr-2 h-4 w-4" />
						Campaign Targeting
					</Button>
					{campaign.status === "active" && (
						<Link href={`/dashboard/campaigns/${campaign.id}/analytics`}>
							<Button variant="outline">
								<BarChart3 className="mr-2 h-4 w-4" />
								Analytics
							</Button>
						</Link>
					)}
					{["pending", "paused", "rejected"].includes(campaign.status) && (
						<Link href={`/dashboard/campaigns/${campaign.id}/edit`}>
							<Button variant="outline">
								<Edit className="mr-2 h-4 w-4" />
								Edit
							</Button>
						</Link>
					)}
				</div>
			</div>

			<div className="grid gap-6 md:grid-cols-3">
				<Card className="md:col-span-2">
					<CardHeader>
						<div className="flex justify-between items-center">
							<div>
								<CardTitle>Campaign Details</CardTitle>
								<CardDescription>
									{campaign.placement?.name || "No placement"} •{" "}
									{new Date(campaign.start_date).toLocaleDateString()} -{" "}
									{new Date(campaign.end_date).toLocaleDateString()}
								</CardDescription>
							</div>
							<Badge
								variant={
									campaign.status === "active"
										? "default"
										: campaign.status === "pending"
										? "outline"
										: "secondary"
								}
								className={cn(
									campaign.status === "active" && "bg-green-100 text-green-800 dark:bg-green-900",
									campaign.status === "paused" && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900",
									campaign.status === "rejected" && "bg-red-100 text-red-800 dark:bg-red-900"
								)}
							>
								{campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
							</Badge>
						</div>
					</CardHeader>
					<CardContent className="space-y-6">
						{primaryAd && (
							<>
								<div>
									<h3 className="text-lg font-medium mb-2">Ad Creative</h3>
									<div className="overflow-hidden rounded-lg border">
										<Image
											src={primaryAd.image_url || "/placeholder.svg"}
											alt="Ad creative"
											width={800}
											height={400}
											className="w-full h-auto"
										/>
									</div>
								</div>

								<div className="grid grid-cols-2 gap-4">
									<div>
										<h3 className="text-sm font-medium">Destination URL</h3>
										<p className="text-sm text-blue-600 underline">{primaryAd.target_url}</p>
									</div>
									<div>
										<h3 className="text-sm font-medium">Created On</h3>
										<p className="text-sm">{new Date(primaryAd.created_at).toLocaleDateString()}</p>
									</div>
								</div>
							</>
						)}

						<Separator />

						{/* Country targeting information */}
						<div>
							<div className="flex items-center gap-2 mb-2">
								<Globe className="h-4 w-4 text-muted-foreground" />
								<h3 className="text-lg font-medium">Country Targeting</h3>
							</div>
							<p className="text-sm">{targetingDescription}</p>
						</div>

						<Separator />

						{/* Page targeting information */}
						<div>
							<div className="flex items-center gap-2 mb-2">
								<FileText className="h-4 w-4 text-muted-foreground" />
								<h3 className="text-lg font-medium">Page Targeting</h3>
							</div>
							<PageTargetingDisplay
								pageTypes={targeting.pageTypes?.types || []}
								categories={targeting.pageTypes?.categories || {}}
								detailed={true}
							/>
						</div>

						{campaign.status === "active" && campaign.impressions !== undefined && (
							<>
								<Separator />
								<div>
									<h3 className="text-lg font-medium mb-4">Performance</h3>
									<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
										<div>
											<p className="text-sm font-medium">Impressions</p>
											<p className="text-2xl font-bold">
												{campaign.impressions.toLocaleString()}
											</p>
										</div>
										<div>
											<p className="text-sm font-medium">Clicks</p>
											<p className="text-2xl font-bold">
												{campaign.clicks?.toLocaleString() || "0"}
											</p>
										</div>
										<div>
											<p className="text-sm font-medium">CTR</p>
											<p className="text-2xl font-bold">
												{campaign.ctr ? `${campaign.ctr}%` : "0.00%"}
											</p>
										</div>
										{primaryAd?.max_impressions && (
											<div>
												<p className="text-sm font-medium">Max Impressions</p>
												<p className="text-2xl font-bold">
													{primaryAd.max_impressions.toLocaleString()}
												</p>
											</div>
										)}
									</div>
								</div>
							</>
						)}

						{primaryAd?.rejection_reason && (
							<>
								<Separator />
								<div>
									<h3 className="text-lg font-medium mb-2">Rejection Reason</h3>
									<p className="text-sm">{primaryAd.rejection_reason}</p>
								</div>
							</>
						)}
					</CardContent>
				</Card>

				<div className="space-y-6">
					{campaign.placement && (
						<Card>
							<CardHeader>
								<CardTitle>Placement Details</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-2">
									<div className="flex justify-between">
										<span className="text-sm font-medium">Placement:</span>
										<span className="text-sm">{campaign.placement.name}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-sm font-medium">Type:</span>
										<span className="text-sm">{campaign.placement.page}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-sm font-medium">Dimensions:</span>
										<span className="text-sm">
											{campaign.placement.width}x{campaign.placement.height}px
										</span>
									</div>
								</div>
							</CardContent>
						</Card>
					)}

					<Card>
						<CardHeader>
							<CardTitle>Budget</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-2">
								{campaign.total_budget && (
									<div className="flex justify-between">
										<span className="text-sm font-medium">Total Budget:</span>
										<span className="text-sm">${campaign.total_budget}</span>
									</div>
								)}
								{campaign.budget_cpc && (
									<div className="flex justify-between">
										<span className="text-sm font-medium">CPC:</span>
										<span className="text-sm">${campaign.budget_cpc}</span>
									</div>
								)}
								{campaign.budget_cpm && (
									<div className="flex justify-between">
										<span className="text-sm font-medium">CPM:</span>
										<span className="text-sm">${campaign.budget_cpm}</span>
									</div>
								)}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<div className="flex justify-between items-center">
								<CardTitle>Campaign Ads ({ads.length})</CardTitle>
								<Button variant="outline" size="sm" onClick={handleAddAd}>
									<Edit className="h-4 w-4 mr-2" />
									Add Ad
								</Button>
							</div>
							<CardDescription>Manage ads within this campaign</CardDescription>
						</CardHeader>
						<CardContent>
							{ads.length === 0 ? (
								<div className="text-center py-8">
									<p className="text-muted-foreground mb-4">No ads in this campaign yet</p>
									<Button variant="outline" onClick={handleAddAd}>
										<Edit className="h-4 w-4 mr-2" />
										Create First Ad
									</Button>
								</div>
							) : (
								<div className="space-y-4">
									{ads.map((ad, index) => (
										<div key={ad.id} className="flex gap-4 items-start p-4 border rounded-lg">
											<div className="relative w-20 h-16 rounded-md overflow-hidden border">
												<Image
													src={ad.image_url || "/placeholder.svg"}
													alt="Ad creative"
													fill
													className="object-cover"
												/>
											</div>
											<div className="flex-1">
												<div className="flex justify-between items-start mb-2">
													<div>
														<p className="font-medium">{ad.title}</p>
														<p className="text-sm text-muted-foreground">
															{index === 0 ? "Primary Ad" : `Ad ${index + 1}`}
														</p>
													</div>
													<Badge variant={ad.status === "active" ? "default" : "secondary"}>
														{ad.status}
													</Badge>
												</div>
												<div className="flex gap-4 text-sm text-muted-foreground mb-3">
													{ad.max_impressions && (
														<span>
															Max Impressions: {ad.max_impressions.toLocaleString()}
														</span>
													)}
													{ad.max_clicks && (
														<span>Max Clicks: {ad.max_clicks.toLocaleString()}</span>
													)}
													<span>Weight: {ad.weight || 1}</span>
												</div>
												<div className="flex gap-2">
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleEditAd(ad.id)}
													>
														<Edit className="h-4 w-4 mr-1" />
														Edit
													</Button>
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleAdAnalytics(ad.id)}
													>
														<BarChart3 className="h-4 w-4 mr-1" />
														Analytics
													</Button>
													{ads.length > 1 && (
														<Button
															variant="outline"
															size="sm"
															onClick={() => handleDeleteAd(ad.id)}
															disabled={deletingAdId === ad.id}
														>
															<Edit className="h-4 w-4 mr-1" />
															{deletingAdId === ad.id ? "Deleting..." : "Delete"}
														</Button>
													)}
												</div>
											</div>
										</div>
									))}
								</div>
							)}
						</CardContent>
					</Card>
				</div>
			</div>
		</div>
	);
}
