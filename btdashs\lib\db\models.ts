// lib/db/models.ts

// Base interface for common fields (e.g., timestamps)
interface BaseModel {
	createdAt?: Date;
	updatedAt?: Date;
}

// Categories table
export interface Category extends BaseModel {
	id: number;
	name: string;
	description?: string;
	icon?: string;
	netuids?: number[];
	active?: boolean;
}

// Subnets table
export interface Subnet extends BaseModel {
	id: number;
	netuid: number;
	name: string;
	owner_address: string;
	description?: string;
	description_short?: string;
	subnet_symbol?: string;
	github_repo?: string;
	website?: string;
	website_perm: string;
	image_url?: string;
	images?: string[];
	main_video_url?: string;
	main_video_type?: string;
	likes_count?: number;
	key_features?: { title: string; description: string }[][];
	github_team_members?: Record<string, any>;
	sub_address_pkey?: string;
	active_validators: number;
	active_miners: number;
	alpha_high: number;
	alpha_low: number;
	white_paper?: string;
	is_active: boolean;
	category_ids?: number[];
	subnet_ids?: number[];
	company_ids?: number[];
	product_ids?: number[];
	news_ids?: number[];
	event_ids?: number[];
	job_ids?: number[];
	updated_at: Date;
}

// Subnet metrics table
export interface SubnetMetric extends BaseModel {
	id: number;
	netuid: number;
	recorded_at: Date;
	max_neurons?: number;
	alpha_token?: string;
	emission?: number;
	emission_percentage?: number;
	recycled_tao?: number;
	registration_cost_tao?: number;
	alpha_price_tao?: number;
	market_cap_tao?: number;
	volume_tao?: number;
	liquidity_tao?: number;
	validators_count?: number;
	staked_tao?: number;
	github_contributions?: Record<string, any>;
	price_change_1_week?: number;
	seven_day_prices?: string | number[];
}

// Network prices table
export interface NetworkPrice extends BaseModel {
	id: number;
	recorded_at: Date;
	price_usd: number;
	price_24h_change?: number;
	volume_24h_usd?: number;
	current_supply?: number;
	total_supply?: number;
	delegated_supply?: number;
	market_cap_usd?: number;
	next_halvening_date?: Date;
	daily_return_per_1000t?: number;
	validating_apy?: number;
	staking_apy?: number;
}

// Network stats table
export interface NetworkStats extends BaseModel {
	id: number;
	total_validators: number;
	total_validators_30d_change?: number;
	active_subnets: number;
	active_subnets_30d_change?: number;
	total_tao: number;
	total_tao_30d_change?: number;
	network_emission: number;
	network_emission_30d_change?: number;
	volume_24h: number;
	transactions_24h: number;
	dominance_btc: number;
	total_staked_tao: number;
	total_staked_tao_30d_change?: number;
	recorded_at: Date;
}

// News table
export interface News extends BaseModel {
	id: number;
	title: string;
	content?: string;
	image_url?: string;
	publication_date: Date;
	source?: string;
	published_status?: boolean;
	category_ids?: number[];
	netuids?: number[];
	company_ids?: number[];
	product_ids?: number[];
	article_part_1?: string;
}

// Validator table
export interface Validator extends BaseModel {
	validator_id: number;
	hotkey: string;
	name: string;
	rank: number;
	system_stake: number;
	stake: number;
	stake_24h_change?: number;
	nominators: number;
	nominators_24h_change?: number;
	nom_24h_per_1000tao?: number;
	weight?: number;
	take?: number;
}

// Validator subnet performance table
export interface ValidatorSubnetPerformance extends BaseModel {
	id: number;
	validator_id: number;
	netuid: number;
	vtrust?: number;
	emissions?: number;
}

// Product table
export interface Product extends BaseModel {
	id: number;
	name: string;
	description?: string;
	subnet_owner?: string;
	launch_date?: Date;
	logo_url?: string;
	website?: string;
	download_url?: string;
	github_repo_url?: string;
	team_members_git: string[];
	team_members_x: string[];
	subnets: number[];
	active: boolean;
	verified: boolean;
	featured: boolean;
	new: boolean;
	clicks: number;
	netuid?: number;
	icon_url?: string;
	company_id?: number;
	category_ids?: number[];
	subnet_ids?: number[];
	company_ids?: number[];
	news_ids?: number[];
	event_ids?: number[];
	jobs_ids?: number[];
	product_ids?: number[];
	hero?: string;
	description_long?: string;
	key_features?: Record<string, any>;
	assets?: string[];
}

// Company table
export interface Company extends BaseModel {
	id: number;
	name: string;
	description?: string | null;
	logo_url?: string | null;
	header_url?: string | null;
	created_at: Date | string;
	updated_at: Date | string;
	locale?: string | null;
	website_url?: string | null;
	is_verified: boolean;
	is_featured: boolean;
	social_media?: { [key: string]: string } | string[] | null;
	netuids: (number | string)[];
	category_ids: (number | string)[];
	news_ids: (number | string)[];
	product_ids: (number | string)[];
	subnet_ids: (number | string)[];
	job_ids: (number | string)[];
	event_ids: (number | string)[];
	foundedyear?: number | string | null;
	teamsize?: string | null;
	location?: string | null;
	hide?: boolean;
}

// Event table
export interface Event extends BaseModel {
	id: number;
	name: string;
	description: string | null;
	start_date: Date;
	end_date: Date | null;
	location: string | null;
	is_virtual: boolean;
	event_type: string;
	website_url: string | null;
	registration_url: string | null;
	image_url: string | null;
	is_published: boolean;
	published_at: Date | null;
	is_featured: boolean;
	created_at: Date;
	updated_at: Date;
	created_by_id: number;
	updated_by_id: number;
	subnet_ids: number[] | null;
	product_ids: number[] | null;
	company_ids: number[] | null;
	category_ids: number[] | null;
	event_ids: number[] | null;
	organizer_ids: number[] | null;
	desc_about_this_event: string | null;
	desc_what_u_will_learn: string | null;
	desc_who_should_attend: string | null;
	image_url_banner: string | null;
	speakers?: string[] | null;
}

// Jobs table
export interface Job extends BaseModel {
	id: number;
	title: string;
	description?: string;
	location?: string;
	remote: boolean;
	type?: string;
	currency?: string;
	salary_time_frame?: string;
	min_salary?: number;
	max_salary?: number;
	published_date?: Date;
	company_id?: number;
	owner_id?: number;
	category_ids?: number[];
	subnet_ids?: number[];
	product_ids?: number[];
	created_at?: Date;
	updated_at?: Date;
	apply_link?: string;
}

// Skills table
export interface Skills extends BaseModel {
	id: number;
	name: string;
}

// User table
export interface User extends BaseModel {
	id: number;
	email: string;
	username: string;
	first_name?: string;
	last_name?: string;
	image_url?: string;
	headline?: string;
	email_verified?: boolean;
	location?: string;
	bio?: string;
	phone?: string;
	expert_job_title?: string;
	authorized_company_admin?: boolean;
	authorized_job_admin?: boolean;
	authorized_events_admin?: boolean;
	website?: string;
	skill_ids?: number[];
	created_at: Date;
	updated_at: Date;
}

// User Skills table
export interface UserSkills extends BaseModel {
	id: number;
	user_id: number;
	skill_id: number;
	endorsements?: number;
	level: "Beginner" | "Intermediate" | "Advanced" | "Expert";
	created_at?: Date;
	updated_at?: Date;
}

// User Preferences table
export interface UserPreferences extends BaseModel {
	id: number;
	user_id: number;
	job_types?: string[];
	industries?: string[];
	travel_availability?: string;
	open_to_relocation?: boolean;
	locations?: string[];
	compensation_range?: string;
	additional_notes?: string;
	created_at?: Date;
	updated_at?: Date;
}

// User Educations table
export interface UserEducation extends BaseModel {
	id: number;
	user_id: number;
	institution_name?: string;
	start_date?: Date;
	end_date?: Date;
	degree?: string;
	description?: string;
	created_at?: Date;
	updated_at?: Date;
	field_of_study?: string;
}

// User Experiences table
export interface UserExperience extends BaseModel {
	id: number;
	user_id: number;
	company_name?: string;
	role?: string;
	location?: string;
	start_date?: Date;
	end_date?: Date;
	description?: string;
	created_at?: Date;
	updated_at?: Date;
	current_job: boolean;
}
