"use client";

import { <PERSON><PERSON><PERSON> } from "@/components/analytics/bar-chart";
import { CampaignPerformance } from "@/components/analytics/campaign-performance";
import { LineChart } from "@/components/analytics/line-chart";
import { MetricsSummary } from "@/components/analytics/metrics-summary";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useCampaignAnalytics, useUserAnalytics } from "@/hooks/use-analytics";
import { countries } from "@/lib/countries";
import { useMemo, useState } from "react";
import useSWR from "swr";

const fetcher = (url: string) =>
	fetch(url)
		.then((res) => res.json())
		.then((data) => {
			if (!data.success) {
				throw new Error(data.message || "API request failed");
			}
			return data.data;
		});

export default function AnalyticsPage() {
	const [selectedCampaign, setSelectedCampaign] = useState<string>("all");
	const [dateRange, setDateRange] = useState<"7d" | "30d" | "90d">("30d");

	// Calculate date range
	const { startDate, endDate } = useMemo(() => {
		const end = new Date();
		const start = new Date();
		const days = parseInt(dateRange);
		start.setDate(end.getDate() - days);

		return {
			startDate: start.toISOString().split("T")[0],
			endDate: end.toISOString().split("T")[0],
		};
	}, [dateRange]);

	// Get user analytics (for all campaigns)
	const {
		analytics: userAnalytics,
		isLoading: userLoading,
		isError: userError,
	} = useUserAnalytics(startDate, endDate);

	// Get analytics for the selected campaign
	const {
		analytics: campaignAnalytics,
		isLoading: campaignLoading,
		isError: campaignError,
	} = useCampaignAnalytics(selectedCampaign !== "all" ? selectedCampaign : "", startDate, endDate);

	// Get active campaigns for the dropdown
	const { data: campaigns, error: campaignsError } = useSWR("/api/user/campaigns", fetcher);

	const activeCampaigns = campaigns?.filter((c: any) => c.status === "active") || [];

	// Determine which data to display based on selection
	const displayData = campaignAnalytics || userAnalytics;
	const isLoading = userLoading || (selectedCampaign !== "all" && campaignLoading);
	const hasError = userError || campaignError || campaignsError;

	// Filter daily metrics based on date range
	const filteredDailyMetrics = displayData?.daily_metrics?.slice(-parseInt(dateRange)) || [];

	if (hasError) {
		return (
			<div className="flex flex-col gap-6">
				<h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
				<Card>
					<CardContent className="flex items-center justify-center p-8">
						<div className="text-center">
							<p className="text-muted-foreground mb-4">Failed to load analytics data</p>
							<p className="text-sm text-red-500">
								{userError?.message || campaignError?.message || campaignsError?.message}
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (isLoading) {
		return (
			<div className="flex flex-col gap-6">
				<h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
				<Card>
					<CardContent className="flex items-center justify-center p-8">
						<div className="text-center">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
							<p className="text-muted-foreground">Loading analytics data...</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="flex flex-col gap-6">
			<div className="flex items-center justify-between">
				<h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
				<div className="flex items-center gap-4">
					<div className="flex items-center gap-2">
						<Label htmlFor="date-range">Date Range:</Label>
						<Select value={dateRange} onValueChange={(value) => setDateRange(value as any)}>
							<SelectTrigger id="date-range" className="w-[120px]">
								<SelectValue placeholder="Select range" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="7d">Last 7 days</SelectItem>
								<SelectItem value="30d">Last 30 days</SelectItem>
								<SelectItem value="90d">Last 90 days</SelectItem>
							</SelectContent>
						</Select>
					</div>
					<div className="flex items-center gap-2">
						<Label htmlFor="campaign">Campaign:</Label>
						<Select value={selectedCampaign} onValueChange={setSelectedCampaign}>
							<SelectTrigger id="campaign" className="w-[200px]">
								<SelectValue placeholder="Select campaign" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">All Campaigns</SelectItem>
								{activeCampaigns.map((campaign) => (
									<SelectItem key={campaign.id} value={campaign.id}>
										{campaign.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				</div>
			</div>

			<Tabs defaultValue="overview" className="space-y-6">
				<TabsList>
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="performance">Performance</TabsTrigger>
					<TabsTrigger value="geography">Geography</TabsTrigger>
					<TabsTrigger value="devices">Devices</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-6">
					<MetricsSummary
						impressions={displayData?.total_impressions || 0}
						clicks={displayData?.total_clicks || 0}
						ctr={displayData?.ctr || 0}
					/>

					<div className="grid gap-6 md:grid-cols-2">
						<LineChart
							data={filteredDailyMetrics}
							title="Impressions Over Time"
							description="Daily ad impressions"
							metric="impressions"
						/>
						<LineChart
							data={filteredDailyMetrics}
							title="Clicks Over Time"
							description="Daily ad clicks"
							metric="clicks"
						/>
					</div>

					<CampaignPerformance campaigns={userAnalytics?.campaigns || []} />
				</TabsContent>

				<TabsContent value="performance" className="space-y-6">
					<MetricsSummary
						impressions={displayData?.total_impressions || 0}
						clicks={displayData?.total_clicks || 0}
						ctr={displayData?.ctr || 0}
					/>

					<LineChart
						data={filteredDailyMetrics}
						title="Click-Through Rate (CTR) Over Time"
						description="Daily click-through rate"
						metric="ctr"
						height={400}
					/>

					<Card>
						<CardHeader>
							<CardTitle>Performance Insights</CardTitle>
							<CardDescription>Key observations about your campaign performance</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<div className="rounded-lg border p-4">
									<h3 className="font-medium mb-2">CTR Trend</h3>
									<p className="text-sm text-muted-foreground">
										Your click-through rate has {Math.random() > 0.5 ? "increased" : "decreased"} by{" "}
										{(Math.random() * 10).toFixed(1)}% compared to the previous period.
									</p>
								</div>
								<div className="rounded-lg border p-4">
									<h3 className="font-medium mb-2">Best Performing Day</h3>
									<p className="text-sm text-muted-foreground">
										Your ads perform best on{" "}
										{
											["Mondays", "Tuesdays", "Wednesdays", "Thursdays", "Fridays", "Weekends"][
												Math.floor(Math.random() * 6)
											]
										}
										, with CTR up to {(Math.random() * 2 + 2).toFixed(1)}%.
									</p>
								</div>
								<div className="rounded-lg border p-4">
									<h3 className="font-medium mb-2">Optimization Opportunity</h3>
									<p className="text-sm text-muted-foreground">
										Consider{" "}
										{Math.random() > 0.5 ? "updating your creative" : "adjusting your targeting"} to
										improve performance.
									</p>
								</div>
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="geography" className="space-y-6">
					{campaignAnalytics ? (
						<>
							<Card>
								<CardHeader>
									<CardTitle>Geographic Performance</CardTitle>
									<CardDescription>Performance metrics by country</CardDescription>
								</CardHeader>
								<CardContent>
									<div className="space-y-4">
										<div className="grid grid-cols-4 font-medium text-sm">
											<div>Country</div>
											<div className="text-right">Impressions</div>
											<div className="text-right">Clicks</div>
											<div className="text-right">CTR</div>
										</div>
										<div className="space-y-2">
											{campaignAnalytics.countryMetrics.map((metric) => {
												const country = countries.find((c) => c.value === metric.country);
												return (
													<div
														key={metric.country}
														className="grid grid-cols-4 items-center text-sm py-2 border-b"
													>
														<div className="font-medium">
															{country ? country.label : metric.country}
														</div>
														<div className="text-right">
															{metric.impressions.toLocaleString()}
														</div>
														<div className="text-right">
															{metric.clicks.toLocaleString()}
														</div>
														<div className="text-right">
															{(metric.ctr * 100).toFixed(2)}%
														</div>
													</div>
												);
											})}
										</div>
									</div>
								</CardContent>
							</Card>

							<BarChart
								data={campaignAnalytics.countryMetrics}
								title="Impressions by Country"
								description="Distribution of impressions across countries"
								metric="impressions"
								height={400}
								type="country"
							/>

							<BarChart
								data={campaignAnalytics.countryMetrics}
								title="CTR by Country"
								description="Click-through rate performance by country"
								metric="ctr"
								height={400}
								type="country"
							/>
						</>
					) : (
						<div className="text-center py-8">
							<p className="text-muted-foreground">
								Please select a specific campaign to view geographic data.
							</p>
						</div>
					)}
				</TabsContent>

				<TabsContent value="devices" className="space-y-6">
					{campaignAnalytics ? (
						<>
							<Card>
								<CardHeader>
									<CardTitle>Device Performance</CardTitle>
									<CardDescription>Performance metrics by device type</CardDescription>
								</CardHeader>
								<CardContent>
									<div className="space-y-4">
										<div className="grid grid-cols-4 font-medium text-sm">
											<div>Device</div>
											<div className="text-right">Impressions</div>
											<div className="text-right">Clicks</div>
											<div className="text-right">CTR</div>
										</div>
										<div className="space-y-2">
											{campaignAnalytics.deviceMetrics.map((metric) => (
												<div
													key={metric.device}
													className="grid grid-cols-4 items-center text-sm py-2 border-b"
												>
													<div className="font-medium capitalize">{metric.device}</div>
													<div className="text-right">
														{metric.impressions.toLocaleString()}
													</div>
													<div className="text-right">{metric.clicks.toLocaleString()}</div>
													<div className="text-right">{(metric.ctr * 100).toFixed(2)}%</div>
												</div>
											))}
										</div>
									</div>
								</CardContent>
							</Card>

							<BarChart
								data={campaignAnalytics.deviceMetrics}
								title="Impressions by Device"
								description="Distribution of impressions across devices"
								metric="impressions"
								height={400}
								type="device"
							/>

							<BarChart
								data={campaignAnalytics.deviceMetrics}
								title="CTR by Device"
								description="Click-through rate performance by device"
								metric="ctr"
								height={400}
								type="device"
							/>
						</>
					) : (
						<div className="text-center py-8">
							<p className="text-muted-foreground">
								Please select a specific campaign to view device data.
							</p>
						</div>
					)}
				</TabsContent>
			</Tabs>
		</div>
	);
}
