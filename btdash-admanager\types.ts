// Define types for our application

export type AdPlacement = {
  id: string
  name: string
  description: string
  type: string
  dimensions: string
  price: number
  priceDisplay: string
  estimatedViews: string
  format: string
  maxFileSize: string
}

export type Campaign = {
  id: string
  name: string
  url: string
  status: "pending" | "approved" | "rejected" | "completed"
  startDate: string
  endDate: string
  imageUrl: string
  placementId: string
  userId: string
  feedback?: string
  impressions: number
  clicks: number
  createdAt: string
  targeting: {
    countries: {
      include: string[]
      exclude: string[]
      mode: "include" | "exclude" | "all"
    }
    pageTypes: {
      types: string[]
      categories: {
        [pageType: string]: string[] | "all"
      }
    }
  }
}

export type User = {
  id: string
  name: string
  email: string
  role: "admin" | "advertiser"
}
