"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/campaigns/[id]/targeting/page",{

/***/ "(app-pages-browser)/./app/dashboard/campaigns/[id]/targeting/page.tsx":
/*!*********************************************************!*\
  !*** ./app/dashboard/campaigns/[id]/targeting/page.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CampaignTargetingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_country_selector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/country-selector */ \"(app-pages-browser)/./components/country-selector.tsx\");\n/* harmony import */ var _components_page_type_selector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/page-type-selector */ \"(app-pages-browser)/./components/page-type-selector.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n// app/dashboard/campaigns/[id]/targeting/page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CampaignTargetingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useParams)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const campaignId = params.id;\n    const [campaign, setCampaign] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    // Targeting state\n    const [targetingMode, setTargetingMode] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"all\");\n    const [includedCountries, setIncludedCountries] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [excludedCountries, setExcludedCountries] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [selectedPageTypes, setSelectedPageTypes] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [selectedCategories, setSelectedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)({});\n    const [selectedDevices, setSelectedDevices] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [selectedInterests, setSelectedInterests] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [selectedAgeRanges, setSelectedAgeRanges] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    // Predefined age ranges\n    const ageRanges = [\n        {\n            min: 18,\n            max: 24,\n            name: \"18-24\"\n        },\n        {\n            min: 25,\n            max: 34,\n            name: \"25-34\"\n        },\n        {\n            min: 35,\n            max: 44,\n            name: \"35-44\"\n        },\n        {\n            min: 45,\n            max: 54,\n            name: \"45-54\"\n        },\n        {\n            min: 55,\n            max: 64,\n            name: \"55-64\"\n        },\n        {\n            min: 65,\n            max: null,\n            name: \"65+\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)({\n        \"CampaignTargetingPage.useEffect\": ()=>{\n            if (campaignId) {\n                fetchCampaignAndTargeting();\n            }\n        }\n    }[\"CampaignTargetingPage.useEffect\"], [\n        campaignId\n    ]);\n    const handlePageTypeChange = (types)=>{\n        setSelectedPageTypes(types);\n        // Clear categories for deselected types\n        setSelectedCategories((prev)=>{\n            const updated = {\n                ...prev\n            };\n            Object.keys(updated).forEach((type)=>{\n                if (!types.includes(type)) {\n                    delete updated[type];\n                }\n            });\n            return updated;\n        });\n    };\n    const fetchCampaignAndTargeting = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch campaign details\n            const campaignResponse = await fetch(\"/api/user/campaigns/\".concat(campaignId));\n            if (!campaignResponse.ok) {\n                throw new Error(\"Failed to fetch campaign: \".concat(campaignResponse.status));\n            }\n            const campaignResult = await campaignResponse.json();\n            if (!campaignResult.success) {\n                throw new Error(campaignResult.message || \"Failed to fetch campaign\");\n            }\n            const campaignData = campaignResult.data;\n            setCampaign(campaignData);\n            // Fetch campaign targeting data\n            const targetingResponse = await fetch(\"/api/user/campaigns/\".concat(campaignId, \"/targeting\"));\n            if (targetingResponse.ok) {\n                const targetingResult = await targetingResponse.json();\n                if (targetingResult.success && targetingResult.data) {\n                    const targeting = targetingResult.data;\n                    // Parse targeting data\n                    if (targeting.countries) {\n                        if (targeting.countries.mode === \"include\") {\n                            setTargetingMode(\"include\");\n                            setIncludedCountries(targeting.countries.include || []);\n                        } else if (targeting.countries.mode === \"exclude\") {\n                            setTargetingMode(\"exclude\");\n                            setExcludedCountries(targeting.countries.exclude || []);\n                        } else {\n                            setTargetingMode(\"all\");\n                        }\n                    }\n                    if (targeting.pageTypes) {\n                        // Filter out invalid page types like \"all\"\n                        const validTypes = (targeting.pageTypes.types || []).filter((type)=>[\n                                \"subnet\",\n                                \"companies\",\n                                \"products\",\n                                \"news\"\n                            ].includes(type));\n                        setSelectedPageTypes(validTypes);\n                        setSelectedCategories(targeting.pageTypes.categories || {});\n                    }\n                    if (targeting.devices) {\n                        setSelectedDevices(targeting.devices || []);\n                    }\n                    if (targeting.languages) {\n                        setSelectedLanguages(targeting.languages || []);\n                    }\n                    if (targeting.interests) {\n                        setSelectedInterests(targeting.interests || []);\n                    }\n                    if (targeting.age && Array.isArray(targeting.age)) {\n                        setSelectedAgeRanges(targeting.age);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Error fetching campaign and targeting:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to fetch campaign\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        if (!campaign) return;\n        try {\n            setSaving(true);\n            // Prepare targeting data\n            const targetingData = {\n                countries: {\n                    mode: targetingMode,\n                    include: targetingMode === \"include\" ? includedCountries : [],\n                    exclude: targetingMode === \"exclude\" ? excludedCountries : []\n                },\n                pageTypes: {\n                    types: selectedPageTypes,\n                    categories: selectedCategories\n                },\n                devices: selectedDevices,\n                languages: selectedLanguages,\n                interests: selectedInterests,\n                age: selectedAgeRanges\n            };\n            const response = await fetch(\"/api/user/campaigns/\".concat(campaignId, \"/targeting\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(targetingData)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(\"Failed to update targeting: \".concat(errorText));\n            }\n            const result = await response.json();\n            if (result.success) {\n                toast({\n                    title: \"Campaign Targeting Updated\",\n                    description: \"Campaign targeting has been successfully updated. All ads in this campaign will use these targeting settings.\"\n                });\n                router.push(\"/dashboard/campaigns/\".concat(campaignId));\n            } else {\n                throw new Error(result.message || \"Failed to update targeting\");\n            }\n        } catch (error) {\n            console.error(\"Error updating targeting:\", error);\n            toast({\n                title: \"Update Failed\",\n                description: error instanceof Error ? error.message : \"Failed to update targeting\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600 dark:text-gray-400\",\n                        children: \"Loading campaign...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n            lineNumber: 207,\n            columnNumber: 4\n        }, this);\n    }\n    if (error || !campaign) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: error || \"Campaign not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>router.push(\"/dashboard/campaigns\"),\n                        children: \"Back to Campaigns\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n            lineNumber: 218,\n            columnNumber: 4\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: ()=>router.back(),\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 7\n                                }, this),\n                                \"Back to Campaign\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-gray-100\",\n                                        children: \"Campaign Targeting\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            'Configure targeting settings for campaign \"',\n                                            campaign.name,\n                                            '\". These settings apply to all ads in this campaign.'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Geographic Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Choose which countries should see your ad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroup, {\n                                            value: targetingMode,\n                                            onValueChange: (v)=>setTargetingMode(v),\n                                            className: \"flex flex-col space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroupItem, {\n                                                            value: \"all\",\n                                                            id: \"all\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"all\",\n                                                            children: \"Show ads to all countries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroupItem, {\n                                                            value: \"include\",\n                                                            id: \"include\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"include\",\n                                                            children: \"Include specific countries only\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroupItem, {\n                                                            value: \"exclude\",\n                                                            id: \"exclude\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"exclude\",\n                                                            children: \"Exclude specific countries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 8\n                                        }, this),\n                                        targetingMode === \"include\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Countries to include\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_country_selector__WEBPACK_IMPORTED_MODULE_1__.CountrySelector, {\n                                                    selected: includedCountries,\n                                                    onChange: setIncludedCountries,\n                                                    placeholder: \"Select countries to include...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 9\n                                        }, this),\n                                        targetingMode === \"exclude\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Countries to exclude\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_country_selector__WEBPACK_IMPORTED_MODULE_1__.CountrySelector, {\n                                                    selected: excludedCountries,\n                                                    onChange: setExcludedCountries,\n                                                    placeholder: \"Select countries to exclude...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Page Type Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Choose which types of pages should display your ad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_type_selector__WEBPACK_IMPORTED_MODULE_2__.PageTypeSelector, {\n                                        selectedTypes: selectedPageTypes,\n                                        selectedCategories: selectedCategories,\n                                        onTypeChange: handlePageTypeChange,\n                                        onCategoryChange: (type, cats)=>setSelectedCategories((prev)=>({\n                                                    ...prev,\n                                                    [type]: cats\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Device Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Target specific device types\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            \"desktop\",\n                                            \"mobile\",\n                                            \"tablet\"\n                                        ].map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: device,\n                                                        checked: selectedDevices.includes(device),\n                                                        onChange: (e)=>{\n                                                            if (e.target.checked) {\n                                                                setSelectedDevices([\n                                                                    ...selectedDevices,\n                                                                    device\n                                                                ]);\n                                                            } else {\n                                                                setSelectedDevices(selectedDevices.filter((d)=>d !== device));\n                                                            }\n                                                        },\n                                                        className: \"rounded border-gray-300 dark:border-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: device,\n                                                        className: \"capitalize\",\n                                                        children: device\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, device, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Language Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Target users by their preferred language\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            \"en\",\n                                            \"es\",\n                                            \"fr\",\n                                            \"de\",\n                                            \"it\",\n                                            \"pt\",\n                                            \"ja\",\n                                            \"ko\",\n                                            \"zh\"\n                                        ].map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: lang,\n                                                        checked: selectedLanguages.includes(lang),\n                                                        onChange: (e)=>{\n                                                            if (e.target.checked) {\n                                                                setSelectedLanguages([\n                                                                    ...selectedLanguages,\n                                                                    lang\n                                                                ]);\n                                                            } else {\n                                                                setSelectedLanguages(selectedLanguages.filter((l)=>l !== lang));\n                                                            }\n                                                        },\n                                                        className: \"rounded border-gray-300 dark:border-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: lang,\n                                                        className: \"uppercase\",\n                                                        children: lang\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, lang, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Interest Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Target users based on their interests\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            \"technology\",\n                                            \"business\",\n                                            \"finance\",\n                                            \"sports\",\n                                            \"entertainment\",\n                                            \"health\",\n                                            \"travel\",\n                                            \"education\"\n                                        ].map((interest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: interest,\n                                                        checked: selectedInterests.includes(interest),\n                                                        onChange: (e)=>{\n                                                            if (e.target.checked) {\n                                                                setSelectedInterests([\n                                                                    ...selectedInterests,\n                                                                    interest\n                                                                ]);\n                                                            } else {\n                                                                setSelectedInterests(selectedInterests.filter((i)=>i !== interest));\n                                                            }\n                                                        },\n                                                        className: \"rounded border-gray-300 dark:border-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: interest,\n                                                        className: \"capitalize\",\n                                                        children: interest\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, interest, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Age Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Target users by age range\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"minAge\",\n                                                        children: \"Minimum Age\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        id: \"minAge\",\n                                                        min: \"13\",\n                                                        max: \"100\",\n                                                        value: ageRange.min || \"\",\n                                                        onChange: (e)=>setAgeRange({\n                                                                ...ageRange,\n                                                                min: e.target.value ? parseInt(e.target.value) : null\n                                                            }),\n                                                        placeholder: \"Min age\",\n                                                        className: \"mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"maxAge\",\n                                                        children: \"Maximum Age\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        id: \"maxAge\",\n                                                        min: \"13\",\n                                                        max: \"100\",\n                                                        value: ageRange.max || \"\",\n                                                        onChange: (e)=>setAgeRange({\n                                                                ...ageRange,\n                                                                max: e.target.value ? parseInt(e.target.value) : null\n                                                            }),\n                                                        placeholder: \"Max age\",\n                                                        className: \"mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSave,\n                                disabled: saving,\n                                className: \"min-w-[140px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 8\n                                    }, this),\n                                    saving ? \"Saving...\" : \"Save Campaign Targeting\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n            lineNumber: 229,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\targeting\\\\page.tsx\",\n        lineNumber: 228,\n        columnNumber: 3\n    }, this);\n}\n_s(CampaignTargetingPage, \"ng74O/iZgPJJBwLX8KZvr6FUOyk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = CampaignTargetingPage;\nvar _c;\n$RefreshReg$(_c, \"CampaignTargetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/campaigns/[id]/targeting/page.tsx\n"));

/***/ })

});