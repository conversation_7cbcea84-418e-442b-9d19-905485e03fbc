"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Loader2, Users } from "lucide-react";
import Link from "next/link";
import { useEffect } from "react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import useSWR from "swr";

const fetcher = (url: string) =>
	fetch(url)
		.then((res) => res.json())
		.then((data) => {
			if (!data.success) {
				throw new Error(data.message || "API request failed");
			}
			return data.data;
		});

export default function AdminDashboard() {
	const { toast } = useToast();

	// Fetch pending campaigns
	const {
		data: pendingCampaigns,
		error: campaignsError,
		isLoading: campaignsLoading,
	} = useSWR("/api/admin/campaigns/pending", fetcher, {
		refreshInterval: 30000,
	});

	// Fetch recent activity
	const {
		data: recentActivity,
		error: activityError,
		isLoading: activityLoading,
	} = useSWR("/api/admin/recent-activity", fetcher, {
		refreshInterval: 30000,
	});

	const loading = campaignsLoading || activityLoading;
	const hasError = campaignsError || activityError;

	useEffect(() => {
		if (hasError) {
			toast({
				title: "Error",
				description: "Failed to load dashboard data. Please try again.",
				variant: "destructive",
			});
		}
	}, [hasError, toast]);

	if (loading) {
		return (
			<div className="container mx-auto flex h-[70vh] items-center justify-center p-6">
				<div className="flex flex-col items-center">
					<Loader2 className="h-12 w-12 animate-spin text-primary" />
					<p className="mt-4 text-lg">Loading dashboard data...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto p-6">
			<h1 className="mb-6 text-3xl font-bold">Admin Dashboard</h1>

			<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
						<BarChart3 className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">$0.00</div>
						<p className="text-xs text-muted-foreground">Platform revenue</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
						<CheckCircle className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">0</div>
						<p className="text-xs text-muted-foreground">Currently running</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Pending Requests</CardTitle>
						<Clock className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{pendingCampaigns?.data?.length || 0}</div>
						<p className="text-xs text-muted-foreground">Awaiting review</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Pending Ads</CardTitle>
						<Users className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">0</div>
						<p className="text-xs text-muted-foreground">Awaiting approval</p>
					</CardContent>
				</Card>
			</div>

			<Tabs defaultValue="overview" className="mt-6">
				<TabsList>
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="analytics">
						<Link href="/admin/analytics">Analytics</Link>
					</TabsTrigger>
					<TabsTrigger value="reports">Reports</TabsTrigger>
				</TabsList>
				<TabsContent value="overview" className="space-y-4">
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
						<Card className="lg:col-span-4">
							<CardHeader>
								<CardTitle>Recent Activity</CardTitle>
								<CardDescription>Overview of recent platform activity</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="space-y-4">
									{recentActivity && recentActivity.length > 0 ? (
										<div className="rounded-md border">
											{recentActivity.map((activity: any, index: number) => (
												<div key={activity.id}>
													<div className="flex items-center justify-between p-4">
														<div>
															<p className="font-medium">{activity.title}</p>
															<p className="text-sm text-muted-foreground">
																{activity.description}
															</p>
														</div>
														<div className="text-sm text-muted-foreground">
															{new Date(activity.created_at).toLocaleDateString() ===
															new Date().toLocaleDateString()
																? new Date(activity.created_at).toLocaleTimeString([], {
																		hour: "2-digit",
																		minute: "2-digit",
																  })
																: new Date(activity.created_at).toLocaleDateString()}
														</div>
													</div>
													{index < recentActivity.length - 1 && <div className="border-t" />}
												</div>
											))}
										</div>
									) : (
										<div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
											<h3 className="text-lg font-medium">No recent activity</h3>
											<p className="text-sm text-muted-foreground">
												Recent admin actions and user registrations will appear here.
											</p>
										</div>
									)}
								</div>
							</CardContent>
							<CardFooter>
								<Button variant="outline" size="sm">
									View All Activity
								</Button>
							</CardFooter>
						</Card>
						<Card className="lg:col-span-3">
							<CardHeader>
								<CardTitle>Pending Requests</CardTitle>
								<CardDescription>Ad campaigns awaiting review</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="space-y-4">
									{pendingCampaigns && pendingCampaigns.length > 0 ? (
										pendingCampaigns.map((campaign: any) => (
											<div key={campaign.id} className="rounded-md border">
												<div className="p-4">
													<div className="flex items-center justify-between">
														<div>
															<p className="font-medium">{campaign.name}</p>
															<p className="text-sm text-muted-foreground">
																Submitted by {campaign.advertiser_name} on{" "}
																{new Date(campaign.created_at).toLocaleDateString()}
															</p>
														</div>
														<Link href={`/admin/requests/${campaign.id}`}>
															<Button variant="ghost" size="sm">
																Review
																<ArrowRight className="ml-2 h-4 w-4" />
															</Button>
														</Link>
													</div>
												</div>
											</div>
										))
									) : (
										<div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
											<h3 className="text-lg font-medium">No pending requests</h3>
											<p className="text-sm text-muted-foreground">
												All ad campaigns have been reviewed.
											</p>
										</div>
									)}
								</div>
							</CardContent>
							<CardFooter>
								<Link href="/admin/requests">
									<Button variant="outline" size="sm">
										View All Requests
									</Button>
								</Link>
							</CardFooter>
						</Card>
					</div>
				</TabsContent>
				<TabsContent value="analytics">
					<Card>
						<CardHeader>
							<CardTitle>Analytics</CardTitle>
							<CardDescription>View detailed analytics and performance metrics</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="flex h-[300px] items-center justify-center rounded-md border border-dashed">
								<Link href="/admin/analytics">
									<Button>Go to Analytics Dashboard</Button>
								</Link>
							</div>
						</CardContent>
					</Card>
				</TabsContent>
				<TabsContent value="reports">
					<Card>
						<CardHeader>
							<CardTitle>Reports</CardTitle>
							<CardDescription>Generate and view campaign reports</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="flex h-[300px] items-center justify-center rounded-md border border-dashed">
								<p className="text-sm text-muted-foreground">Reports dashboard coming soon</p>
							</div>
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
