// src/presentation/controllers/adsController.js
const path = require("path");
const AdsService = require("../../application/services/AdsService");
const UserService = require("../../application/services/UserService");
const AnalyticsService = require("../../application/services/AnalyticsService");
const BudgetService = require("../../application/services/BudgetService");
const FileUploadService = require("../../application/services/FileUploadService");
const TargetingService = require("../../application/services/TargetingService");
const CampaignTargetingService = require("../../application/services/CampaignTargetingService");
const NotificationService = require("../../application/services/NotificationService");
const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");
const {
	sendSuccess,
	sendError,
	sendNotFound,
	sendUnauthorized,
	sendForbidden,
	sendInternalError,
} = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

// ─── CAMPAIGNS ────────────────────────────────────────────────────────────────

const getAllCampaigns = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await UserService.isAdminUser(auth0_id);
	const campaigns = await AdsService.getAllCampaigns(user.id, isAdmin);

	return sendSuccess(res, campaigns, "Campaigns retrieved successfully");
});

const getCampaignById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const auth0_id = req.auth?.sub;

	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await UserService.isAdminUser(auth0_id);
	const campaign = await AdsService.getCampaignById(id, user.id, isAdmin);

	if (!campaign) {
		return sendNotFound(res, "Campaign not found");
	}

	return sendSuccess(res, campaign, "Campaign retrieved successfully");
});

const createCampaign = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const { advertiser_id, name, total_budget, budget_cpc, budget_cpm, start_date, end_date } = req.body;

	const campaignData = {
		advertiser_id: advertiser_id || user.id,
		manager_id: user.id,
		name,
		total_budget: total_budget || null,
		budget_cpc: budget_cpc || null,
		budget_cpm: budget_cpm || null,
		start_date: new Date(start_date),
		end_date: new Date(end_date),
		status: "pending",
		// Note: targeting is handled separately via ad_targets table
	};

	const newCampaign = await AdsService.createCampaign(campaignData);

	return sendSuccess(res, newCampaign, "Campaign created successfully", 201);
});

const updateCampaign = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const auth0_id = req.auth?.sub;

	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const { name, total_budget, budget_cpc, budget_cpm, start_date, end_date } = req.body;

	const updateData = {
		name,
		total_budget,
		budget_cpc,
		budget_cpm,
		start_date: start_date ? new Date(start_date) : undefined,
		end_date: end_date ? new Date(end_date) : undefined,
	};

	const isAdmin = await AdsService.isAdminUser(auth0_id);
	const updatedCampaign = await AdsService.updateCampaign(id, updateData, user.id, isAdmin);

	return sendSuccess(res, updatedCampaign, "Campaign updated successfully");
});

const updateCampaignStatus = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const { status } = req.body;
	if (!["pending", "active", "paused", "rejected", "completed"].includes(status)) {
		return sendError(res, "Invalid status", null, 400);
	}

	const [updatedCampaign] = await db("dtm_ads.ad_campaigns")
		.where({ id: req.params.id })
		.update({
			status,
			updated_at: new Date(),
		})
		.returning("*");

	if (!updatedCampaign) {
		return sendNotFound(res, "Campaign not found");
	}

	return sendSuccess(res, updatedCampaign, "Campaign status updated successfully");
});

const deleteCampaign = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const auth0_id = req.auth?.sub;

	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await AdsService.isAdminUser(auth0_id);

	// Check if campaign exists and user has access
	const campaign = await AdsService.getCampaignById(id, user.id, isAdmin);
	if (!campaign) {
		return sendNotFound(res, "Campaign not found or access denied");
	}

	// Check if campaign has been paid for and calculate refund
	const totalSpent = parseFloat(campaign.total_budget || 0);
	let refundAmount = 0;

	if (totalSpent > 0) {
		// For now, no refund policy - user loses the money
		// TODO: Implement proper refund logic based on campaign performance
		logger.warn("Campaign deletion with paid budget - no refund issued", {
			campaignId: id,
			userId: user.id,
			totalBudget: totalSpent,
		});
	}

	// Delete campaign (this will cascade delete ads and targeting)
	await AdsService.deleteCampaign(id, user.id, isAdmin);

	return sendSuccess(
		res,
		{
			deleted: true,
			refundAmount,
			message:
				totalSpent > 0
					? "Campaign deleted successfully. No refund issued for paid campaigns."
					: "Campaign deleted successfully.",
		},
		"Campaign deleted successfully"
	);
});

// ─── ADS ──────────────────────────────────────────────────────────────────────

const getAllAds = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await UserService.isAdminUser(auth0_id);
	const ads = await AdsService.getAdsWithCampaigns(user.id, isAdmin);

	return sendSuccess(res, ads, "Ads retrieved successfully");
});

const getCampaignAds = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const auth0_id = req.auth?.sub;

	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await UserService.isAdminUser(auth0_id);

	// First check if user has access to this campaign
	const campaign = await AdsService.getCampaignById(id, user.id, isAdmin);
	if (!campaign) {
		return sendNotFound(res, "Campaign not found or access denied");
	}

	const ads = await AdsService.getAdsByCampaignId(id, user.id, isAdmin);

	return sendSuccess(res, ads, "Campaign ads retrieved successfully");
});

const getAdById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const auth0_id = req.auth?.sub;

	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await UserService.isAdminUser(auth0_id);

	// Get ad with authorization check
	const ad = await AdsService.getAdWithCampaignById(id, user.id, isAdmin);
	if (!ad) {
		return sendNotFound(res, "Ad not found or access denied");
	}

	return sendSuccess(res, ad, "Ad retrieved successfully");
});

const createAd = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await db("dtm_base.users").where({ auth0_user_id: auth0_id }).first().timeout(5000);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const { campaign_id, slot_id, title, image_url, target_url, max_impressions, max_clicks, weight } = req.body;

	// Validate required fields
	const requiredFields = { campaign_id, slot_id, title, image_url, target_url };
	const missingFields = Object.entries(requiredFields)
		.filter(([_, value]) => !value)
		.map(([key]) => key);

	if (missingFields.length > 0) {
		return sendError(res, "Missing required fields", { missingFields }, 400);
	}

	// Use transaction for safety
	const [newAd] = await db.transaction(async (trx) => {
		return await trx("dtm_ads.ads")
			.insert({
				campaign_id,
				slot_id,
				title,
				image_url,
				target_url,
				max_impressions: max_impressions || null,
				max_clicks: max_clicks || null,
				weight: weight || 100,
				status: "pending",
				created_at: new Date(),
				updated_at: new Date(),
			})
			.returning("*");
	});

	return sendSuccess(res, newAd, "Ad created successfully", 201);
});

const updateAd = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const auth0_id = req.auth?.sub;

	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await UserService.isAdminUser(auth0_id);

	// Get ad with authorization check
	const ad = await AdsService.getAdWithCampaignById(id, user.id, isAdmin);
	if (!ad) {
		return sendNotFound(res, "Ad not found or access denied");
	}

	const { title, image_url, target_url, max_impressions, max_clicks, weight } = req.body;

	const updateData = {
		title,
		image_url,
		target_url,
		max_impressions,
		max_clicks,
		weight,
		updated_at: new Date(),
	};

	const [updatedAd] = await db("dtm_ads.ads").where({ id: req.params.id }).update(updateData).returning("*");

	return sendSuccess(res, updatedAd, "Ad updated successfully");
});

const updateAdStatus = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const { status } = req.body;
	if (!["pending", "active", "rejected", "paused"].includes(status)) {
		return sendError(res, "Invalid status", null, 400);
	}

	const [updatedAd] = await db("dtm_ads.ads")
		.where({ id: req.params.id })
		.update({
			status,
			updated_at: new Date(),
		})
		.returning("*");

	if (!updatedAd) {
		return sendNotFound(res, "Ad not found");
	}

	return sendSuccess(res, updatedAd, "Ad status updated successfully");
});

const deleteAd = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const auth0_id = req.auth?.sub;

	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await UserService.isAdminUser(auth0_id);

	// Get ad with authorization check
	const ad = await AdsService.getAdWithCampaignById(id, user.id, isAdmin);
	if (!ad) {
		return sendNotFound(res, "Ad not found or access denied");
	}

	await db("dtm_ads.ads").where({ id: req.params.id }).delete();

	return sendSuccess(res, null, "Ad deleted successfully");
});

// ─── SLOTS ────────────────────────────────────────────────────────────────────

const getAllSlots = asyncHandler(async (req, res) => {
	const slots = await AdsService.getAllSlots();

	return sendSuccess(res, slots, "Ad slots retrieved successfully");
});

const getSlotById = asyncHandler(async (req, res) => {
	const slot = await db("dtm_ads.ad_slots").where({ id: req.params.id, is_active: true }).first();

	if (!slot) {
		return sendNotFound(res, "Slot not found or inactive");
	}

	return sendSuccess(res, slot, "Ad slot retrieved successfully");
});

// ─── AD SERVING & TRACKING ───────────────────────────────────────────────────

const serveAd = asyncHandler(async (req, res) => {
	const { slot, country_code, device_type, language, user_agent } = req.query;
	if (!slot) {
		return sendError(res, "Slot parameter required", null, 400);
	}

	// Get eligible ads for this slot
	const eligibleAds = await db("dtm_ads.ads as a")
		.join("dtm_ads.ad_campaigns as c", "a.campaign_id", "c.id")
		.where({
			"a.slot_id": slot,
			"a.status": "active",
			"c.status": "active",
		})
		.where("c.start_date", "<=", new Date())
		.where("c.end_date", ">=", new Date())
		.select("a.*");

	if (eligibleAds.length === 0) {
		return sendNotFound(res, "No ads available for this slot");
	}

	// Filter ads based on campaign-level targeting
	const campaignTargetingService = new CampaignTargetingService();
	const userContext = {
		country_code,
		device_type,
		language,
		user_agent,
	};

	const targetedAds = [];
	// Group ads by campaign to check targeting once per campaign
	const adsByCampaign = {};
	for (const ad of eligibleAds) {
		if (!adsByCampaign[ad.campaign_id]) {
			adsByCampaign[ad.campaign_id] = [];
		}
		adsByCampaign[ad.campaign_id].push(ad);
	}

	// Check targeting for each campaign and include all ads from matching campaigns
	for (const [campaignId, campaignAds] of Object.entries(adsByCampaign)) {
		const matches = await campaignTargetingService.matchesTargeting(parseInt(campaignId), userContext);
		if (matches) {
			targetedAds.push(...campaignAds);
		}
	}

	// Use targeted ads if available, otherwise fall back to all eligible ads
	const adsToSelect = targetedAds.length > 0 ? targetedAds : eligibleAds;

	// Weighted random selection
	const totalWeight = adsToSelect.reduce((sum, ad) => sum + ad.weight, 0);
	let random = Math.random() * totalWeight;
	let selectedAd;

	for (const ad of adsToSelect) {
		random -= ad.weight;
		if (random <= 0) {
			selectedAd = ad;
			break;
		}
	}

	return sendSuccess(res, selectedAd, "Ad served successfully");
});

const trackImpression = asyncHandler(async (req, res) => {
	const { ad_id, session_id } = req.body;
	const ip_address = req.ip;
	const user_agent = req.headers["user-agent"];

	// Get user ID if authenticated
	let user_id = null;
	if (req.auth?.sub) {
		const user = await db("dtm_base.users").where({ auth0_user_id: req.auth.sub }).first();
		user_id = user?.id || null;
	}

	// Record the impression
	await db("dtm_ads.ad_impressions").insert({
		ad_id,
		user_id,
		session_id,
		ip_address,
		user_agent,
		created_at: new Date(),
	});

	// Process real-time spend tracking
	try {
		const budgetService = new BudgetService();
		const spendResult = await budgetService.processRealTimeSpend(ad_id, "impression");

		if (!spendResult.success && spendResult.reason === "insufficient_funds") {
			logger.warn("Campaign paused due to insufficient funds during impression tracking", {
				ad_id,
				campaign_paused: spendResult.campaign_paused,
			});
		}
	} catch (spendError) {
		// Log spend tracking error but don't fail the impression tracking
		logger.error("Error processing spend for impression", { error: spendError, ad_id });
	}

	return sendSuccess(res, null, "Impression tracked successfully");
});

const trackClick = asyncHandler(async (req, res) => {
	const { adId } = req.params;
	const session_id = req.query.session_id;
	const ip_address = req.ip;
	const user_agent = req.headers["user-agent"];

	// Get user ID if authenticated
	let user_id = null;
	if (req.auth?.sub) {
		const user = await db("dtm_base.users").where({ auth0_user_id: req.auth.sub }).first();
		user_id = user?.id || null;
	}

	// Record the click
	await db("dtm_ads.ad_clicks").insert({
		ad_id: adId,
		user_id,
		session_id,
		ip_address,
		user_agent,
		created_at: new Date(),
	});

	// Process real-time spend tracking for clicks
	try {
		const budgetService = new BudgetService();
		const spendResult = await budgetService.processRealTimeSpend(adId, "click");

		if (!spendResult.success && spendResult.reason === "insufficient_funds") {
			logger.warn("Campaign paused due to insufficient funds during click tracking", {
				ad_id: adId,
				campaign_paused: spendResult.campaign_paused,
			});
		}
	} catch (spendError) {
		// Log spend tracking error but don't fail the click tracking
		logger.error("Error processing spend for click", { error: spendError, ad_id: adId });
	}

	// Get the ad's target URL
	const ad = await db("dtm_ads.ads").where({ id: adId }).first();

	if (!ad) {
		return sendNotFound(res, "Ad not found");
	}

	// Redirect to the target URL
	res.redirect(ad.target_url);
});

// ─── ANALYTICS ────────────────────────────────────────────────────────────────

const getCampaignAnalytics = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const { id: campaignId } = req.params;
	const { start_date, end_date } = req.query;

	// Verify user owns this campaign
	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaignId, advertiser_id: user.id }).first();

	if (!campaign) {
		return sendNotFound(res, "Campaign not found or access denied");
	}

	const analyticsService = new AnalyticsService();
	const analytics = await analyticsService.getCampaignAnalytics(campaignId, start_date, end_date);

	return sendSuccess(res, analytics, "Campaign analytics retrieved successfully");
});

const getAdAnalytics = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const { id: adId } = req.params;
	const { start_date, end_date } = req.query;

	// Verify user owns this ad
	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await UserService.isAdminUser(auth0_id);

	// Get ad with authorization check
	const ad = await AdsService.getAdWithCampaignById(adId, user.id, isAdmin);
	if (!ad) {
		return sendNotFound(res, "Ad not found or access denied");
	}

	const analyticsService = new AnalyticsService();
	const analytics = await analyticsService.getAdAnalytics(adId, start_date, end_date);

	return sendSuccess(res, analytics, "Ad analytics retrieved successfully");
});

const getUserAnalytics = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const { start_date, end_date } = req.query;

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const analyticsService = new AnalyticsService();
	const analytics = await analyticsService.getUserAnalytics(user.id, start_date, end_date);

	return sendSuccess(res, analytics, "User analytics retrieved successfully");
});

const getConversionTracking = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const { id: campaignId } = req.params;
	const { start_date, end_date } = req.query;

	// Verify user owns this campaign (check manager_id, not advertiser_id)
	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaignId, manager_id: user.id }).first();

	if (!campaign) {
		return sendNotFound(res, "Campaign not found or access denied");
	}

	const analyticsService = new AnalyticsService();
	const conversions = await analyticsService.getConversionTracking(campaignId, start_date, end_date);

	return sendSuccess(res, conversions, "Conversion tracking retrieved successfully");
});

// ─── BUDGET MANAGEMENT ────────────────────────────────────────────────────────

const getAdvertiserBalance = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const budgetService = new BudgetService();
	const balance = await budgetService.getAdvertiserBalance(user.id);

	return sendSuccess(res, balance, "Advertiser balance retrieved successfully");
});

const addFunds = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const { amount, description, payment_method } = req.body;

	if (!amount || amount <= 0) {
		return sendError(res, "Valid amount is required", null, 400);
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const budgetService = new BudgetService();
	const result = await budgetService.addFunds(user.id, amount, description || "Funds added", payment_method);

	return sendSuccess(res, result, "Funds added successfully");
});

const getSpendingHistory = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const { limit = 50, offset = 0 } = req.query;

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const budgetService = new BudgetService();
	const transactions = await budgetService.getSpendingHistory(user.id, parseInt(limit), parseInt(offset));

	return sendSuccess(res, transactions, "Spending history retrieved successfully");
});

const getCampaignSpendSummary = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const { id: campaignId } = req.params;

	// Verify user owns this campaign
	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaignId, advertiser_id: user.id }).first();

	if (!campaign) {
		return sendNotFound(res, "Campaign not found or access denied");
	}

	const budgetService = new BudgetService();
	const spendSummary = await budgetService.getCampaignSpendSummary(campaignId);

	return sendSuccess(res, spendSummary, "Campaign spend summary retrieved successfully");
});

// ─── FILE UPLOAD ──────────────────────────────────────────────────────────────

const uploadAdImage = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	if (!req.file) {
		return sendError(res, "No file uploaded", null, 400);
	}

	const { slot_id } = req.body;
	let slotRequirements = {};

	// Get slot requirements if slot_id provided
	if (slot_id) {
		const slot = await db("dtm_ads.ad_slots").where({ id: slot_id }).first();
		if (slot) {
			slotRequirements = {
				width: slot.width,
				height: slot.height,
				maxFileSize: slot.max_file_size,
			};
		}
	}

	const fileUploadService = new FileUploadService();

	// Validate file
	const validation = await fileUploadService.validateFile(req.file, slotRequirements);
	if (!validation.isValid) {
		return sendError(res, "File validation failed", { errors: validation.errors }, 400);
	}

	// Validate image content for security
	const isValidImage = await fileUploadService.validateImageContent(req.file.buffer);
	if (!isValidImage) {
		return sendError(res, "Invalid image file", null, 400);
	}

	// Process and save file
	const fileInfo = await fileUploadService.processAndSaveFile(req.file, {
		optimize: true,
		quality: 85,
		resize:
			slotRequirements.width && slotRequirements.height
				? {
						width: slotRequirements.width,
						height: slotRequirements.height,
						fit: "cover",
				  }
				: null,
	});

	// Generate thumbnail
	const thumbnail = await fileUploadService.generateThumbnail(fileInfo.filepath, {
		width: 150,
		height: 150,
		quality: 80,
	});

	const result = {
		...fileInfo,
		thumbnail,
	};

	return sendSuccess(res, result, "Image uploaded successfully");
});

const deleteAdImage = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const { filename } = req.params;

	if (!filename) {
		return sendError(res, "Filename is required", null, 400);
	}

	const fileUploadService = new FileUploadService();

	// Delete main file
	const mainDeleted = await fileUploadService.deleteFile(filename);

	// Try to delete thumbnail (if exists)
	const fileExtension = path.extname(filename);
	const baseName = path.basename(filename, fileExtension);
	const thumbnailFilename = `${baseName}_thumb${fileExtension}`;
	const thumbnailDeleted = await fileUploadService.deleteFile(thumbnailFilename);

	if (mainDeleted) {
		return sendSuccess(
			res,
			{
				deleted: true,
				thumbnail_deleted: thumbnailDeleted,
			},
			"Image deleted successfully"
		);
	} else {
		return sendError(res, "Failed to delete image", null, 500);
	}
});

const getImageInfo = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const { filename } = req.params;

	if (!filename) {
		return sendError(res, "Filename is required", null, 400);
	}

	try {
		const fileUploadService = new FileUploadService();
		const fileInfo = await fileUploadService.getFileInfo(filename);

		return sendSuccess(res, fileInfo, "Image info retrieved successfully");
	} catch (error) {
		return sendNotFound(res, "Image not found");
	}
});

// ─── TARGETING ────────────────────────────────────────────────────────────────

const setAdTargeting = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const { id: adId } = req.params;
	const targetingRules = req.body;

	// Verify user owns this ad
	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await AdsService.isAdminUser(auth0_id);

	// Get ad with authorization check
	const ad = await AdsService.getAdWithCampaignById(adId, user.id, isAdmin);
	if (!ad) {
		return sendNotFound(res, "Ad not found or access denied");
	}

	const targetingService = new TargetingService();
	const rules = await targetingService.createTargetingRules(adId, targetingRules);

	return sendSuccess(res, rules, "Targeting rules set successfully");
});

const getAdTargeting = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const { id: adId } = req.params;

	// Verify user owns this ad
	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const isAdmin = await UserService.isAdminUser(auth0_id);

	// Get ad with authorization check
	const ad = await AdsService.getAdWithCampaignById(adId, user.id, isAdmin);
	if (!ad) {
		return sendNotFound(res, "Ad not found or access denied");
	}

	const targetingService = new TargetingService();
	const targetingRules = await targetingService.getTargetingRules(adId);

	return sendSuccess(res, targetingRules, "Targeting rules retrieved successfully");
});

const getTargetingOptions = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const campaignTargetingService = new CampaignTargetingService();
	const options = await campaignTargetingService.getTargetingOptions();

	return sendSuccess(res, options, "Targeting options retrieved successfully");
});

// Campaign-level targeting endpoints
const setCampaignTargeting = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const { campaignId } = req.params;
	const { targetingRules } = req.body;

	if (!campaignId || !targetingRules) {
		return sendBadRequest(res, "Campaign ID and targeting rules are required");
	}

	// Check if user owns this campaign or is admin
	const isAdmin = await AdsService.isAdminUser(user.id);
	const campaign = await AdsService.getCampaignById(campaignId, user.id, isAdmin);

	if (!campaign) {
		return sendNotFound(res, "Campaign not found or access denied");
	}

	const campaignTargetingService = new CampaignTargetingService();
	const updatedTargeting = await campaignTargetingService.setTargetingRules(campaignId, targetingRules);

	return sendSuccess(res, updatedTargeting, "Campaign targeting rules set successfully");
});

const getCampaignTargeting = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const { campaignId } = req.params;

	if (!campaignId) {
		return sendBadRequest(res, "Campaign ID is required");
	}

	// Check if user owns this campaign or is admin
	const isAdmin = await AdsService.isAdminUser(user.id);
	const campaign = await AdsService.getCampaignById(campaignId, user.id, isAdmin);

	if (!campaign) {
		return sendNotFound(res, "Campaign not found or access denied");
	}

	const campaignTargetingService = new CampaignTargetingService();
	const targetingRules = await campaignTargetingService.getTargetingRules(campaignId);

	return sendSuccess(res, targetingRules, "Campaign targeting rules retrieved successfully");
});

// ─── NOTIFICATIONS ────────────────────────────────────────────────────────────

const getUserNotifications = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const { limit = 50, offset = 0, unread_only = false, type } = req.query;

	const notificationService = new NotificationService();
	const notifications = await notificationService.getUserNotifications(user.id, {
		limit: parseInt(limit),
		offset: parseInt(offset),
		unreadOnly: unread_only === "true",
		type,
	});

	return sendSuccess(res, notifications, "Notifications retrieved successfully");
});

const markNotificationRead = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const { id: notificationId } = req.params;

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const notificationService = new NotificationService();
	const success = await notificationService.markAsRead(notificationId, user.id);

	if (success) {
		return sendSuccess(res, { id: notificationId }, "Notification marked as read");
	} else {
		return sendNotFound(res, "Notification not found");
	}
});

const markAllNotificationsRead = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const notificationService = new NotificationService();
	const count = await notificationService.markAllAsRead(user.id);

	return sendSuccess(res, { markedCount: count }, "All notifications marked as read");
});

const sendTestNotification = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const user = await AdsService.getUserByAuth0Id(auth0_id);
	if (!user) {
		return sendNotFound(res, "User not found");
	}

	const { type = "test", title = "Test Notification", message = "This is a test notification." } = req.body;

	const notificationService = new NotificationService();
	const result = await notificationService.sendNotification({
		user_id: user.id,
		type,
		title,
		message,
		email: {
			enabled: true,
			subject: title,
			template: "default",
		},
	});

	return sendSuccess(res, result, "Test notification sent");
});

module.exports = {
	// Campaigns
	getAllCampaigns,
	getCampaignById,
	createCampaign,
	updateCampaign,
	updateCampaignStatus,
	deleteCampaign,

	// Ads
	getAllAds,
	getCampaignAds,
	getAdById,
	createAd,
	updateAd,
	updateAdStatus,
	deleteAd,

	// Slots
	getAllSlots,
	getSlotById,

	// Serving & Tracking
	serveAd,
	trackImpression,
	trackClick,

	// Analytics
	getCampaignAnalytics,
	getAdAnalytics,
	getUserAnalytics,
	getConversionTracking,

	// Budget Management
	getAdvertiserBalance,
	addFunds,
	getSpendingHistory,
	getCampaignSpendSummary,

	// File Upload
	uploadAdImage,
	deleteAdImage,
	getImageInfo,

	// Targeting (Ad-level - deprecated)
	setAdTargeting,
	getAdTargeting,

	// Campaign Targeting (New)
	setCampaignTargeting,
	getCampaignTargeting,
	getTargetingOptions,

	// Notifications
	getUserNotifications,
	markNotificationRead,
	markAllNotificationsRead,
	sendTestNotification,
};
