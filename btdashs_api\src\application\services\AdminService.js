const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");
const NotificationService = require("./NotificationService");

class AdminService {
	/**
	 * Get all pending campaigns for admin review
	 * @param {number} limit - Number of campaigns to return
	 * @param {number} offset - Offset for pagination
	 * @returns {Promise<Array>} Pending campaigns
	 */
	async getPendingCampaigns(limit = 50, offset = 0) {
		try {
			const campaigns = await db("dtm_ads.ad_campaigns")
				.leftJoin("dtm_base.companies", "ad_campaigns.advertiser_id", "companies.id")
				.leftJoin("dtm_base.users as manager", "ad_campaigns.manager_id", "manager.id")
				.where("ad_campaigns.status", "pending")
				.select(
					"ad_campaigns.*",
					"companies.name as advertiser_name",
					db.raw(
						"CONCAT(COALESCE(manager.first_name, ''), ' ', COALESCE(manager.last_name, '')) as manager_name"
					),
					"manager.email as manager_email"
				)
				.orderBy("ad_campaigns.created_at", "asc")
				.limit(limit)
				.offset(offset);

			return campaigns;
		} catch (error) {
			logger.error("Error getting pending campaigns", { error });
			throw error;
		}
	}

	/**
	 * Get all approved campaigns for admin review
	 * @param {number} limit - Number of campaigns to return
	 * @param {number} offset - Offset for pagination
	 * @returns {Promise<Array>} Approved campaigns
	 */
	async getApprovedCampaigns(limit = 50, offset = 0) {
		try {
			const campaigns = await db("dtm_ads.ad_campaigns")
				.leftJoin("dtm_base.companies", "ad_campaigns.advertiser_id", "companies.id")
				.leftJoin("dtm_base.users as manager", "ad_campaigns.manager_id", "manager.id")
				.where("ad_campaigns.status", "active")
				.select(
					"ad_campaigns.*",
					"companies.name as advertiser_name",
					db.raw(
						"CONCAT(COALESCE(manager.first_name, ''), ' ', COALESCE(manager.last_name, '')) as manager_name"
					),
					"manager.email as manager_email"
				)
				.orderBy("ad_campaigns.updated_at", "desc")
				.limit(limit)
				.offset(offset);

			return campaigns;
		} catch (error) {
			logger.error("Error getting approved campaigns", { error });
			throw error;
		}
	}

	/**
	 * Get all rejected campaigns for admin review
	 * @param {number} limit - Number of campaigns to return
	 * @param {number} offset - Offset for pagination
	 * @returns {Promise<Array>} Rejected campaigns
	 */
	async getRejectedCampaigns(limit = 50, offset = 0) {
		try {
			const campaigns = await db("dtm_ads.ad_campaigns")
				.leftJoin("dtm_base.companies", "ad_campaigns.advertiser_id", "companies.id")
				.leftJoin("dtm_base.users as manager", "ad_campaigns.manager_id", "manager.id")
				.where("ad_campaigns.status", "rejected")
				.select(
					"ad_campaigns.*",
					"companies.name as advertiser_name",
					db.raw(
						"CONCAT(COALESCE(manager.first_name, ''), ' ', COALESCE(manager.last_name, '')) as manager_name"
					),
					"manager.email as manager_email"
				)
				.orderBy("ad_campaigns.updated_at", "desc")
				.limit(limit)
				.offset(offset);

			return campaigns;
		} catch (error) {
			logger.error("Error getting rejected campaigns", { error });
			throw error;
		}
	}

	/**
	 * Get all pending ads for admin review
	 * @param {number} limit - Number of ads to return
	 * @param {number} offset - Offset for pagination
	 * @returns {Promise<Array>} Pending ads
	 */
	async getPendingAds(limit = 50, offset = 0) {
		try {
			const ads = await db("dtm_ads.ads")
				.leftJoin("dtm_ads.ad_campaigns", "ads.campaign_id", "ad_campaigns.id")
				.leftJoin("dtm_base.users as manager", "ad_campaigns.manager_id", "manager.id")
				.leftJoin("dtm_base.companies", "ad_campaigns.advertiser_id", "companies.id")
				.where("ads.status", "pending")
				.select(
					"ads.*",
					"ad_campaigns.name as campaign_name",
					db.raw(
						"CONCAT(COALESCE(manager.first_name, ''), ' ', COALESCE(manager.last_name, '')) as manager_name"
					),
					"manager.email as manager_email",
					"companies.name as advertiser_name"
				)
				.orderBy("ads.created_at", "asc")
				.limit(limit)
				.offset(offset);

			return ads;
		} catch (error) {
			logger.error("Error getting pending ads", { error });
			throw error;
		}
	}

	/**
	 * Approve a campaign
	 * @param {number} campaignId - Campaign ID
	 * @param {number} adminId - Admin user ID
	 * @param {string} notes - Optional approval notes
	 * @returns {Promise<Object>} Updated campaign
	 */
	async approveCampaign(campaignId, adminId, notes = null) {
		const trx = await db.transaction();

		try {
			// Get campaign details first
			const campaign = await trx("dtm_ads.ad_campaigns").where({ id: campaignId }).first();

			if (!campaign) {
				throw new Error("Campaign not found");
			}

			// Check if campaign has a budget that requires payment
			if (campaign.total_budget && campaign.total_budget > 0) {
				// Update campaign status to approved (payment pending)
				const [updatedCampaign] = await trx("dtm_ads.ad_campaigns")
					.where({ id: campaignId })
					.update({
						status: "approved",
						updated_at: new Date(),
					})
					.returning("*");

				// Create billing transaction for the campaign budget
				const BudgetService = require("./BudgetService");
				const budgetService = new BudgetService();

				// Create a pending billing transaction
				await trx("dtm_ads.billing_transactions").insert({
					advertiser_id: campaign.advertiser_id,
					amount: campaign.total_budget,
					currency: "USD",
					description: `Campaign payment for "${campaign.name}" (Admin Approved)`,
					status: "pending",
					payment_method: "stripe",
					campaign_id: campaignId,
					created_at: new Date(),
					updated_at: new Date(),
				});

				// Create approval notification
				const notificationService = new NotificationService();
				await notificationService.sendNotification({
					user_id: updatedCampaign.advertiser_id,
					type: "campaign_approval",
					title: "Campaign Approved - Payment Required",
					message: `Your campaign "${updatedCampaign.name}" has been approved. Please complete payment to activate your campaign.`,
					related_id: campaignId,
					metadata: { admin_id: adminId, notes, payment_required: true },
					email: {
						enabled: true,
						subject: "Campaign Approved - Payment Required - BTDash",
						template: "campaign_approval_payment",
						templateData: {
							campaignName: updatedCampaign.name,
							totalBudget: campaign.total_budget,
							notes,
						},
					},
				});

				// Log admin action
				await this.logAdminAction(trx, adminId, "campaign_approval", campaignId, {
					notes,
					payment_required: true,
					budget: campaign.total_budget,
				});

				await trx.commit();

				logger.info("Campaign approved with payment required", {
					campaignId,
					adminId,
					notes,
					budget: campaign.total_budget,
				});
				return updatedCampaign;
			} else {
				// No budget required, activate immediately
				const [updatedCampaign] = await trx("dtm_ads.ad_campaigns")
					.where({ id: campaignId })
					.update({
						status: "active",
						updated_at: new Date(),
					})
					.returning("*");

				// Create approval notification
				const notificationService = new NotificationService();
				await notificationService.sendNotification({
					user_id: updatedCampaign.advertiser_id,
					type: "campaign_approval",
					title: "Campaign Approved",
					message: `Your campaign "${updatedCampaign.name}" has been approved and is now active.`,
					related_id: campaignId,
					metadata: { admin_id: adminId, notes },
					email: {
						enabled: true,
						subject: "Campaign Approved - BTDash",
						template: "campaign_approval",
						templateData: {
							campaignName: updatedCampaign.name,
							notes,
						},
					},
				});

				// Log admin action
				await this.logAdminAction(trx, adminId, "campaign_approval", campaignId, { notes });

				await trx.commit();

				logger.info("Campaign approved and activated", { campaignId, adminId, notes });
				return updatedCampaign;
			}
		} catch (error) {
			await trx.rollback();
			logger.error("Error approving campaign", { error, campaignId, adminId });
			throw error;
		}
	}

	/**
	 * Reject a campaign
	 * @param {number} campaignId - Campaign ID
	 * @param {number} adminId - Admin user ID
	 * @param {string} reason - Rejection reason
	 * @param {string} notes - Additional notes
	 * @returns {Promise<Object>} Updated campaign
	 */
	async rejectCampaign(campaignId, adminId, reason, notes = null) {
		const trx = await db.transaction();

		try {
			// Update campaign status
			const [updatedCampaign] = await trx("dtm_ads.ad_campaigns")
				.where({ id: campaignId })
				.update({
					status: "rejected",
					updated_at: new Date(),
				})
				.returning("*");

			if (!updatedCampaign) {
				throw new Error("Campaign not found");
			}

			// Create rejection notification using NotificationService
			const notificationService = new NotificationService();
			await notificationService.sendNotification({
				user_id: updatedCampaign.advertiser_id,
				type: "campaign_rejection",
				title: "Campaign Rejected",
				message: `Your campaign "${updatedCampaign.name}" has been rejected. Reason: ${reason}`,
				related_id: campaignId,
				metadata: { admin_id: adminId, reason, notes },
				email: {
					enabled: true,
					subject: "Campaign Rejected - BTDash",
					template: "campaign_rejection",
					templateData: {
						campaignName: updatedCampaign.name,
						reason,
						notes,
					},
				},
			});

			// Log admin action
			await this.logAdminAction(trx, adminId, "campaign_rejection", campaignId, { reason, notes });

			await trx.commit();

			logger.info("Campaign rejected", { campaignId, adminId, reason });
			return updatedCampaign;
		} catch (error) {
			await trx.rollback();
			logger.error("Error rejecting campaign", { error, campaignId, adminId });
			throw error;
		}
	}

	/**
	 * Approve an ad
	 * @param {number} adId - Ad ID
	 * @param {number} adminId - Admin user ID
	 * @param {string} notes - Optional approval notes
	 * @returns {Promise<Object>} Updated ad
	 */
	async approveAd(adId, adminId, notes = null) {
		const trx = await db.transaction();

		try {
			// Get ad with campaign info
			const ad = await trx("dtm_ads.ads")
				.leftJoin("dtm_ads.ad_campaigns", "ads.campaign_id", "ad_campaigns.id")
				.where("ads.id", adId)
				.select("ads.*", "ad_campaigns.advertiser_id")
				.first();

			if (!ad) {
				throw new Error("Ad not found");
			}

			// Update ad status
			const [updatedAd] = await trx("dtm_ads.ads")
				.where({ id: adId })
				.update({
					status: "active",
					updated_at: new Date(),
				})
				.returning("*");

			// Create approval notification
			await this.createNotification(
				trx,
				ad.advertiser_id,
				"ad_approval",
				"Ad Approved",
				`Your ad "${ad.title}" has been approved and is now active.`,
				adId,
				{ admin_id: adminId, notes }
			);

			// Log admin action
			await this.logAdminAction(trx, adminId, "ad_approval", adId, { notes });

			await trx.commit();

			logger.info("Ad approved", { adId, adminId, notes });
			return updatedAd;
		} catch (error) {
			await trx.rollback();
			logger.error("Error approving ad", { error, adId, adminId });
			throw error;
		}
	}

	/**
	 * Reject an ad
	 * @param {number} adId - Ad ID
	 * @param {number} adminId - Admin user ID
	 * @param {string} reason - Rejection reason
	 * @param {string} notes - Additional notes
	 * @returns {Promise<Object>} Updated ad
	 */
	async rejectAd(adId, adminId, reason, notes = null) {
		const trx = await db.transaction();

		try {
			// Get ad with campaign info
			const ad = await trx("dtm_ads.ads")
				.leftJoin("dtm_ads.ad_campaigns", "ads.campaign_id", "ad_campaigns.id")
				.where("ads.id", adId)
				.select("ads.*", "ad_campaigns.advertiser_id")
				.first();

			if (!ad) {
				throw new Error("Ad not found");
			}

			// Update ad status and rejection reason
			const [updatedAd] = await trx("dtm_ads.ads")
				.where({ id: adId })
				.update({
					status: "rejected",
					rejection_reason: reason,
					updated_at: new Date(),
				})
				.returning("*");

			// Create rejection notification
			await this.createNotification(
				trx,
				ad.advertiser_id,
				"ad_rejection",
				"Ad Rejected",
				`Your ad "${ad.title}" has been rejected. Reason: ${reason}`,
				adId,
				{ admin_id: adminId, reason, notes }
			);

			// Log admin action
			await this.logAdminAction(trx, adminId, "ad_rejection", adId, { reason, notes });

			await trx.commit();

			logger.info("Ad rejected", { adId, adminId, reason });
			return updatedAd;
		} catch (error) {
			await trx.rollback();
			logger.error("Error rejecting ad", { error, adId, adminId });
			throw error;
		}
	}

	/**
	 * Get rejection reasons
	 * @param {string} entityType - 'campaign' or 'ad'
	 * @returns {Promise<Array>} Available rejection reasons
	 */
	async getRejectionReasons(entityType) {
		try {
			const reasons = await db("dtm_ads.rejection_reasons")
				.where({ entity_type: entityType, is_active: true })
				.select("code", "description")
				.orderBy("description");

			return reasons;
		} catch (error) {
			logger.error("Error getting rejection reasons", { error, entityType });
			throw error;
		}
	}

	/**
	 * Create a notification
	 * @param {Object} trx - Database transaction
	 * @param {number} userId - User ID
	 * @param {string} type - Notification type
	 * @param {string} title - Notification title
	 * @param {string} message - Notification message
	 * @param {number} relatedId - Related entity ID
	 * @param {Object} metadata - Additional metadata
	 * @returns {Promise<Object>} Created notification
	 */
	async createNotification(trx, userId, type, title, message, relatedId = null, metadata = null) {
		try {
			const [notification] = await trx("dtm_ads.ad_notifications")
				.insert({
					user_id: userId,
					type,
					title,
					message,
					is_read: false,
					related_id: relatedId,
					metadata,
					created_at: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			return notification;
		} catch (error) {
			logger.error("Error creating notification", { error, userId, type });
			throw error;
		}
	}

	/**
	 * Log admin action
	 * @param {Object} trx - Database transaction
	 * @param {number} adminId - Admin user ID
	 * @param {string} action - Action type
	 * @param {number} entityId - Entity ID
	 * @param {Object} metadata - Additional metadata
	 * @returns {Promise<void>}
	 */
	async logAdminAction(trx, adminId, action, entityId, metadata = null) {
		try {
			await trx("dtm_ads.admin_actions").insert({
				admin_id: adminId,
				action,
				entity_id: entityId,
				metadata,
				created_at: new Date(),
			});
		} catch (error) {
			logger.error("Error logging admin action", { error, adminId, action, entityId });
			// Don't throw error for logging failure
		}
	}
}

module.exports = AdminService;
