"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { BarChart3, CreditCard, ImageIcon, Layout, LayoutDashboard, Settings, ShoppingCart } from "lucide-react"

interface NavProps {
  isAdmin?: boolean
}

export function DashboardNav({ isAdmin = false }: NavProps) {
  const pathname = usePathname()

  const advertiserRoutes = [
    {
      href: "/dashboard",
      icon: LayoutDashboard,
      title: "Dashboard",
    },
    {
      href: "/dashboard/placements",
      icon: Layout,
      title: "Ad Placements",
    },
    {
      href: "/dashboard/campaigns",
      icon: ImageIcon,
      title: "My Campaigns",
    },
    {
      href: "/dashboard/analytics",
      icon: BarChart3,
      title: "Analytics",
    },
    {
      href: "/dashboard/billing",
      icon: CreditCard,
      title: "Billing",
    },
    {
      href: "/dashboard/settings",
      icon: Settings,
      title: "Settings",
    },
  ]

  const adminRoutes = [
    {
      href: "/admin",
      icon: LayoutDashboard,
      title: "Dashboard",
    },
    {
      href: "/admin/requests",
      icon: ShoppingCart,
      title: "Ad Requests",
    },
    {
      href: "/admin/inventory",
      icon: Layout,
      title: "Inventory",
    },
    {
      href: "/admin/analytics",
      icon: BarChart3,
      title: "Analytics",
    },
    {
      href: "/admin/settings",
      icon: Settings,
      title: "Settings",
    },
  ]

  const routes = isAdmin ? adminRoutes : advertiserRoutes

  return (
    <nav className="grid items-start gap-2">
      {routes.map((route) => (
        <Link
          key={route.href}
          href={route.href}
          className={cn(
            "group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
            pathname === route.href ? "bg-accent text-accent-foreground" : "transparent",
          )}
        >
          <route.icon className="mr-2 h-4 w-4" />
          <span>{route.title}</span>
        </Link>
      ))}
    </nav>
  )
}
