import type { AdPlacement, Campaign, User } from "@/types"

// Mock data
let placements: AdPlacement[] = [
  {
    id: "1",
    name: "Homepage Banner",
    description: "Premium placement at the top of our homepage",
    type: "Website",
    dimensions: "1200 x 300 px",
    price: 499,
    priceDisplay: "$499/month",
    estimatedViews: "50,000+",
    format: "JPG, PNG, GIF",
    maxFileSize: "2MB",
  },
  {
    id: "2",
    name: "Sidebar Ad",
    description: "Visible on all article pages",
    type: "Website",
    dimensions: "300 x 600 px",
    price: 299,
    priceDisplay: "$299/month",
    estimatedViews: "75,000+",
    format: "JPG, PNG, GIF",
    maxFileSize: "1MB",
  },
  {
    id: "3",
    name: "Newsletter Ad",
    description: "Featured in our weekly newsletter",
    type: "Newsletter",
    dimensions: "600 x 200 px",
    price: 199,
    priceDisplay: "$199/week",
    estimatedViews: "25,000+",
    format: "JPG, PNG",
    maxFileSize: "500KB",
  },
  {
    id: "4",
    name: "In-App Banner",
    description: "Displayed at the bottom of the app screen",
    type: "Mobile App",
    dimensions: "320 x 50 px",
    price: 399,
    priceDisplay: "$399/month",
    estimatedViews: "100,000+",
    format: "JPG, PNG",
    maxFileSize: "300KB",
  },
]

// Update the mock campaigns data to include targeting information
let campaigns: Campaign[] = [
  {
    id: "1",
    name: "Summer Sale Banner",
    url: "https://example.com/summer-sale",
    status: "approved",
    startDate: "2025-06-01",
    endDate: "2025-06-30",
    imageUrl: "/placeholder.svg?height=300&width=1200",
    placementId: "1",
    userId: "user1",
    impressions: 8432,
    clicks: 342,
    createdAt: "2025-05-15",
    targeting: {
      countries: {
        include: ["US", "CA", "UK"],
        exclude: [],
        mode: "include",
      },
      pageTypes: {
        types: ["news", "products"],
        categories: {
          news: ["news-tech", "news-business"],
          products: "all",
        },
      },
    },
  },
  {
    id: "2",
    name: "New Product Launch",
    url: "https://example.com/new-product",
    status: "pending",
    startDate: "2025-07-01",
    endDate: "2025-07-15",
    imageUrl: "/placeholder.svg?height=200&width=600",
    placementId: "3",
    userId: "user1",
    impressions: 0,
    clicks: 0,
    createdAt: "2025-06-20",
    targeting: {
      countries: {
        include: [],
        exclude: ["CN", "RU"],
        mode: "exclude",
      },
      pageTypes: {
        types: ["companies", "products"],
        categories: {
          companies: ["companies-tech", "companies-finance"],
          products: ["products-hardware", "products-software"],
        },
      },
    },
  },
  {
    id: "3",
    name: "Holiday Special",
    url: "https://example.com/holiday",
    status: "approved",
    startDate: "2025-12-01",
    endDate: "2025-12-31",
    imageUrl: "/placeholder.svg?height=600&width=300",
    placementId: "2",
    userId: "user1",
    impressions: 3802,
    clicks: 231,
    createdAt: "2025-11-15",
    targeting: {
      countries: {
        include: [],
        exclude: [],
        mode: "all",
      },
      pageTypes: {
        types: ["subnet", "news", "companies", "products"],
        categories: {
          subnet: "all",
          news: "all",
          companies: "all",
          products: "all",
        },
      },
    },
  },
  // Add a new pending campaign for testing the review flow
  {
    id: "pending-123",
    name: "Spring Promotion 2025",
    url: "https://example.com/spring-promo",
    status: "pending",
    startDate: "2025-04-01",
    endDate: "2025-04-30",
    imageUrl: "/placeholder.svg?height=300&width=1200",
    placementId: "1",
    userId: "user1",
    impressions: 0,
    clicks: 0,
    createdAt: new Date().toISOString(),
    targeting: {
      countries: {
        include: ["US", "CA", "MX", "BR"],
        exclude: [],
        mode: "include",
      },
      pageTypes: {
        types: ["news", "products", "companies"],
        categories: {
          news: ["news-tech", "news-business", "news-finance"],
          products: ["products-hardware", "products-software"],
          companies: "all",
        },
      },
    },
  },
  // Add an overlapping campaign for testing conflict detection
  {
    id: "overlap-test",
    name: "Early Summer Promotion",
    url: "https://example.com/early-summer",
    status: "approved",
    startDate: "2025-05-15",
    endDate: "2025-06-15",
    imageUrl: "/placeholder.svg?height=300&width=1200",
    placementId: "1", // Same placement as "Summer Sale Banner"
    userId: "user1",
    impressions: 5230,
    clicks: 187,
    createdAt: "2025-04-20",
    targeting: {
      countries: {
        include: ["US", "CA"],
        exclude: [],
        mode: "include",
      },
      pageTypes: {
        types: ["news", "products"],
        categories: {
          news: ["news-tech"],
          products: ["products-software"],
        },
      },
    },
  },
  // Add a new test campaign with obvious conflict for testing
  {
    id: "conflict-test-pending",
    name: "⚠️ TEST: Conflict Campaign",
    url: "https://example.com/conflict-test",
    status: "pending",
    startDate: "2025-06-10", // Overlaps with both Summer Sale Banner and Early Summer Promotion
    endDate: "2025-06-20",
    imageUrl: "/placeholder.svg?height=300&width=1200",
    placementId: "1", // Same placement as existing campaigns
    userId: "user1",
    impressions: 0,
    clicks: 0,
    createdAt: new Date().toISOString(),
    targeting: {
      countries: {
        include: ["US", "UK", "DE", "FR"],
        exclude: [],
        mode: "include",
      },
      pageTypes: {
        types: ["news", "products"],
        categories: {
          news: ["news-tech", "news-business"],
          products: ["products-software"],
        },
      },
    },
  },
]

const users: User[] = [
  {
    id: "user1",
    name: "John Doe",
    email: "<EMAIL>",
    role: "advertiser",
  },
  {
    id: "admin1",
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
  },
]

// Let's add a debug function to help diagnose issues with the mock database
export function debugMockDb() {
  console.log("All campaigns:", campaigns)
  console.log(
    "Pending campaigns:",
    campaigns.filter((c) => c.status === "pending"),
  )
  console.log(
    "Approved campaigns:",
    campaigns.filter((c) => c.status === "approved"),
  )
  console.log(
    "Rejected campaigns:",
    campaigns.filter((c) => c.status === "rejected"),
  )
}

// Helper function to check if two date ranges overlap
function datesOverlap(startDate1: string, endDate1: string, startDate2: string, endDate2: string): boolean {
  const start1 = new Date(startDate1).getTime()
  const end1 = new Date(endDate1).getTime()
  const start2 = new Date(startDate2).getTime()
  const end2 = new Date(endDate2).getTime()

  return start1 <= end2 && start2 <= end1
}

// Mock database functions
export const db = {
  placements: {
    getAll: () => [...placements],
    getById: (id: string) => placements.find((p) => p.id === id),
    getByType: (type: string) => placements.filter((p) => p.type === type),
    create: (placement: Omit<AdPlacement, "id">) => {
      const newPlacement = { ...placement, id: `placement_${Date.now()}` }
      placements.push(newPlacement)
      return newPlacement
    },
    update: (id: string, data: Partial<AdPlacement>) => {
      const index = placements.findIndex((p) => p.id === id)
      if (index !== -1) {
        placements[index] = { ...placements[index], ...data }
        return placements[index]
      }
      return null
    },
    delete: (id: string) => {
      placements = placements.filter((p) => p.id !== id)
      return true
    },
  },
  campaigns: {
    getAll: () => [...campaigns],
    getById: (id: string) => campaigns.find((c) => c.id === id),
    getByUserId: (userId: string) => campaigns.filter((c) => c.userId === userId),
    getByStatus: (status: Campaign["status"]) => {
      const filteredCampaigns = campaigns.filter((c) => c.status === status)
      console.log(`Filtered campaigns by status '${status}':`, filteredCampaigns)
      return [...filteredCampaigns] // Return a copy to avoid mutation issues
    },
    create: (campaign: Omit<Campaign, "id">) => {
      const newCampaign = { ...campaign, id: `campaign_${Date.now()}` }
      campaigns.push(newCampaign)
      return newCampaign
    },
    update: (id: string, data: Partial<Campaign>) => {
      const index = campaigns.findIndex((c) => c.id === id)
      if (index !== -1) {
        campaigns[index] = { ...campaigns[index], ...data }
        return campaigns[index]
      }
      return null
    },
    delete: (id: string) => {
      campaigns = campaigns.filter((c) => c.id !== id)
      return true
    },
    debug: debugMockDb,
    // Add a function to find overlapping campaigns
    findOverlappingCampaigns: (campaign: Campaign) => {
      return campaigns.filter(
        (c) =>
          c.id !== campaign.id && // Not the same campaign
          c.status === "approved" && // Only check approved campaigns
          c.placementId === campaign.placementId && // Same placement
          datesOverlap(c.startDate, c.endDate, campaign.startDate, campaign.endDate), // Date overlap
      )
    },
  },
  users: {
    getAll: () => [...users],
    getById: (id: string) => users.find((u) => u.id === id),
    getByEmail: (email: string) => users.find((u) => u.email === email),
    create: (user: Omit<User, "id">) => {
      const newUser = { ...user, id: `user_${Date.now()}` }
      users.push(newUser)
      return newUser
    },
  },
}
