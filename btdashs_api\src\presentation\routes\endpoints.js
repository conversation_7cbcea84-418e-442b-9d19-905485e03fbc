// src/presentation/routes/endpoints.js - API endpoints documentation

const express = require("express");
const router = express.Router();

/**
 * GET /api/endpoints - Comprehensive API endpoints reference
 */
router.get("/", (req, res) => {
	const endpointsHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BTDash API Endpoints Reference</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8f9fa;
      color: #333;
      line-height: 1.6;
    }
    
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem;
      text-align: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .header h1 {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }
    
    .header p {
      font-size: 1.1rem;
      opacity: 0.9;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }
    
    .nav-links {
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      text-align: center;
    }
    
    .nav-links a {
      display: inline-block;
      margin: 0 1rem;
      padding: 0.5rem 1rem;
      background: #667eea;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      transition: background 0.3s;
    }
    
    .nav-links a:hover {
      background: #5a6fd8;
    }
    
    .section {
      background: white;
      border-radius: 8px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .section h2 {
      color: #667eea;
      margin-bottom: 1.5rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #e9ecef;
    }
    
    .endpoint-group {
      margin-bottom: 2rem;
    }
    
    .endpoint-group h3 {
      color: #495057;
      margin-bottom: 1rem;
      font-size: 1.3rem;
    }
    
    .endpoint {
      display: flex;
      align-items: center;
      padding: 1rem;
      margin-bottom: 0.5rem;
      background: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #667eea;
    }
    
    .method {
      font-weight: bold;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      margin-right: 1rem;
      min-width: 60px;
      text-align: center;
      font-size: 0.8rem;
    }
    
    .method.get { background: #28a745; color: white; }
    .method.post { background: #007bff; color: white; }
    .method.put { background: #ffc107; color: #212529; }
    .method.patch { background: #17a2b8; color: white; }
    .method.delete { background: #dc3545; color: white; }
    
    .path {
      font-family: 'Courier New', monospace;
      font-weight: bold;
      margin-right: 1rem;
      flex: 1;
    }
    
    .description {
      color: #6c757d;
      flex: 2;
    }
    
    .auth-required {
      background: #ffeaa7;
      color: #2d3436;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.8rem;
      margin-left: 1rem;
    }
    
    .auth-optional {
      background: #81ecec;
      color: #2d3436;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.8rem;
      margin-left: 1rem;
    }
    
    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }
    
    .stat-card {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border-top: 4px solid #667eea;
    }
    
    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      color: #667eea;
    }
    
    .stat-label {
      color: #6c757d;
      margin-top: 0.5rem;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>BTDash API Endpoints</h1>
    <p>Comprehensive reference for all available API endpoints</p>
  </div>
  
  <div class="container">
    <div class="nav-links">
      <a href="/api/docs">📚 Interactive Swagger Docs</a>
      <a href="/api/docs/openapi.json">📄 OpenAPI Spec</a>
      <a href="/monitoring/health">🏥 Health Check</a>
      <a href="/monitoring/dashboard">📊 Monitoring Dashboard</a>
    </div>
    
    <div class="stats">
      <div class="stat-card">
        <div class="stat-number">50+</div>
        <div class="stat-label">Total Endpoints</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">8</div>
        <div class="stat-label">Main Categories</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">Auth0</div>
        <div class="stat-label">Authentication</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">REST</div>
        <div class="stat-label">API Style</div>
      </div>
    </div>

    <div class="section">
      <h2>🔐 Authentication & Users</h2>
      <div class="endpoint-group">
        <h3>User Management</h3>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/user/me</span>
          <span class="description">Get current user profile</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
        <div class="endpoint">
          <span class="method post">POST</span>
          <span class="path">/api/user/sync</span>
          <span class="description">Sync user from Auth0</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
        <div class="endpoint">
          <span class="method put">PUT</span>
          <span class="path">/api/user/me</span>
          <span class="description">Update user profile</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/user/company</span>
          <span class="description">Get user's company associations</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
        <div class="endpoint">
          <span class="method post">POST</span>
          <span class="path">/api/user/company</span>
          <span class="description">Create/join company</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>🏢 Companies</h2>
      <div class="endpoint-group">
        <h3>Company Directory</h3>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/companies</span>
          <span class="description">Get all companies</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/companies/:id</span>
          <span class="description">Get company by ID</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method post">POST</span>
          <span class="path">/api/companies</span>
          <span class="description">Create new company</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
        <div class="endpoint">
          <span class="method put">PUT</span>
          <span class="path">/api/companies/:id</span>
          <span class="description">Update company</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
        <div class="endpoint">
          <span class="method delete">DELETE</span>
          <span class="path">/api/companies/:id</span>
          <span class="description">Delete company</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>💼 Jobs</h2>
      <div class="endpoint-group">
        <h3>Job Board</h3>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/jobs</span>
          <span class="description">Get all job postings</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/jobs/:id</span>
          <span class="description">Get job by ID</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method post">POST</span>
          <span class="path">/api/jobs</span>
          <span class="description">Create new job posting</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
        <div class="endpoint">
          <span class="method put">PUT</span>
          <span class="path">/api/jobs/:id</span>
          <span class="description">Update job posting</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
        <div class="endpoint">
          <span class="method delete">DELETE</span>
          <span class="path">/api/jobs/:id</span>
          <span class="description">Delete job posting</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>📅 Events</h2>
      <div class="endpoint-group">
        <h3>Event Management</h3>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/events</span>
          <span class="description">Get all events</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/events/:id</span>
          <span class="description">Get event by ID</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method post">POST</span>
          <span class="path">/api/events</span>
          <span class="description">Create new event</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
        <div class="endpoint">
          <span class="method put">PUT</span>
          <span class="path">/api/events/:id</span>
          <span class="description">Update event</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
        <div class="endpoint">
          <span class="method delete">DELETE</span>
          <span class="path">/api/events/:id</span>
          <span class="description">Delete event</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>🏷️ Categories & Skills</h2>
      <div class="endpoint-group">
        <h3>Content Organization</h3>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/categories</span>
          <span class="description">Get all categories</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/categories/:id</span>
          <span class="description">Get category by ID</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/skills</span>
          <span class="description">Get all skills</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/products</span>
          <span class="description">Get all products</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>📊 Network & Analytics</h2>
      <div class="endpoint-group">
        <h3>Blockchain Data</h3>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/subnets</span>
          <span class="description">Get subnet information</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/validators</span>
          <span class="description">Get validator data</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/network-stats</span>
          <span class="description">Get network statistics</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/network-prices</span>
          <span class="description">Get network price data</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>📰 Content & News</h2>
      <div class="endpoint-group">
        <h3>Content Management</h3>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/news</span>
          <span class="description">Get news articles</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/api/ads</span>
          <span class="description">Get advertisements</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method post">POST</span>
          <span class="path">/api/ads</span>
          <span class="description">Create advertisement</span>
          <span class="auth-required">🔒 Auth Required</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>🔧 System & Monitoring</h2>
      <div class="endpoint-group">
        <h3>Health & Monitoring</h3>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/health</span>
          <span class="description">Basic health check</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/monitoring/health</span>
          <span class="description">Detailed health metrics</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/monitoring/metrics</span>
          <span class="description">Application metrics</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>
          <span class="path">/monitoring/dashboard</span>
          <span class="description">Monitoring dashboard</span>
          <span class="auth-optional">🔓 Public</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>📖 Authentication Guide</h2>
      <p><strong>Base URL:</strong> <code>https://api.btdash.com</code> (Production) | <code>http://localhost:3001</code> (Development)</p>
      <p><strong>Authentication:</strong> JWT Bearer tokens from Auth0</p>
      <p><strong>Content-Type:</strong> <code>application/json</code></p>
      
      <h3>Example Request with Authentication:</h3>
      <pre style="background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto;">
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
     -H "Content-Type: application/json" \\
     https://api.btdash.com/api/user/me
      </pre>
      
      <h3>Response Format:</h3>
      <pre style="background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto;">
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
      </pre>
    </div>
  </div>
</body>
</html>`;

	res.send(endpointsHtml);
});

/**
 * GET /api/endpoints/json - API endpoints in JSON format
 */
router.get("/json", (req, res) => {
	const endpoints = {
		baseUrl: process.env.NODE_ENV === "production" ? "https://api.btdash.com" : "http://localhost:3001",
		authentication: "JWT Bearer tokens from Auth0",
		contentType: "application/json",
		categories: {
			authentication: {
				name: "Authentication & Users",
				endpoints: [
					{ method: "GET", path: "/api/user/me", description: "Get current user profile", auth: "required" },
					{ method: "POST", path: "/api/user/sync", description: "Sync user from Auth0", auth: "required" },
					{ method: "PUT", path: "/api/user/me", description: "Update user profile", auth: "required" },
					{
						method: "GET",
						path: "/api/user/company",
						description: "Get user company associations",
						auth: "required",
					},
					{ method: "POST", path: "/api/user/company", description: "Create/join company", auth: "required" },
				],
			},
			companies: {
				name: "Companies",
				endpoints: [
					{ method: "GET", path: "/api/companies", description: "Get all companies", auth: "optional" },
					{ method: "GET", path: "/api/companies/:id", description: "Get company by ID", auth: "optional" },
					{ method: "POST", path: "/api/companies", description: "Create new company", auth: "required" },
					{ method: "PUT", path: "/api/companies/:id", description: "Update company", auth: "required" },
					{ method: "DELETE", path: "/api/companies/:id", description: "Delete company", auth: "required" },
				],
			},
			jobs: {
				name: "Jobs",
				endpoints: [
					{ method: "GET", path: "/api/jobs", description: "Get all job postings", auth: "optional" },
					{ method: "GET", path: "/api/jobs/:id", description: "Get job by ID", auth: "optional" },
					{ method: "POST", path: "/api/jobs", description: "Create new job posting", auth: "required" },
					{ method: "PUT", path: "/api/jobs/:id", description: "Update job posting", auth: "required" },
					{ method: "DELETE", path: "/api/jobs/:id", description: "Delete job posting", auth: "required" },
				],
			},
			events: {
				name: "Events",
				endpoints: [
					{ method: "GET", path: "/api/events", description: "Get all events", auth: "optional" },
					{ method: "GET", path: "/api/events/:id", description: "Get event by ID", auth: "optional" },
					{ method: "POST", path: "/api/events", description: "Create new event", auth: "required" },
					{ method: "PUT", path: "/api/events/:id", description: "Update event", auth: "required" },
					{ method: "DELETE", path: "/api/events/:id", description: "Delete event", auth: "required" },
				],
			},
			content: {
				name: "Categories & Content",
				endpoints: [
					{ method: "GET", path: "/api/categories", description: "Get all categories", auth: "optional" },
					{ method: "GET", path: "/api/categories/:id", description: "Get category by ID", auth: "optional" },
					{ method: "GET", path: "/api/skills", description: "Get all skills", auth: "optional" },
					{ method: "GET", path: "/api/products", description: "Get all products", auth: "optional" },
					{ method: "GET", path: "/api/news", description: "Get news articles", auth: "optional" },
					{ method: "GET", path: "/api/ads", description: "Get advertisements", auth: "optional" },
					{ method: "POST", path: "/api/ads", description: "Create advertisement", auth: "required" },
					{
						method: "GET",
						path: "/api/campaigns/:id/analytics",
						description: "Get campaign analytics",
						auth: "required",
					},
					{
						method: "GET",
						path: "/api/ads/:id/analytics",
						description: "Get ad analytics",
						auth: "required",
					},
					{ method: "GET", path: "/api/user/analytics", description: "Get user analytics", auth: "required" },
					{
						method: "GET",
						path: "/api/campaigns/:id/conversions",
						description: "Get conversion tracking",
						auth: "required",
					},
					{
						method: "GET",
						path: "/api/user/balance",
						description: "Get advertiser balance",
						auth: "required",
					},
					{
						method: "POST",
						path: "/api/user/add-funds",
						description: "Add funds to balance",
						auth: "required",
					},
					{
						method: "GET",
						path: "/api/user/spending-history",
						description: "Get spending history",
						auth: "required",
					},
					{
						method: "GET",
						path: "/api/campaigns/:id/spend-summary",
						description: "Get campaign spend summary",
						auth: "required",
					},
					{ method: "POST", path: "/api/upload/image", description: "Upload ad image", auth: "required" },
					{
						method: "DELETE",
						path: "/api/upload/image/:filename",
						description: "Delete ad image",
						auth: "required",
					},
					{
						method: "GET",
						path: "/api/upload/image/:filename/info",
						description: "Get image info",
						auth: "required",
					},
					{
						method: "POST",
						path: "/api/ads/:id/targeting",
						description: "Set ad targeting",
						auth: "required",
					},
					{
						method: "GET",
						path: "/api/ads/:id/targeting",
						description: "Get ad targeting",
						auth: "required",
					},
					{
						method: "GET",
						path: "/api/targeting/options",
						description: "Get targeting options",
						auth: "required",
					},
					{
						method: "GET",
						path: "/api/user/notifications",
						description: "Get user notifications",
						auth: "required",
					},
					{
						method: "PUT",
						path: "/api/user/notifications/:id/read",
						description: "Mark notification as read",
						auth: "required",
					},
					{
						method: "PUT",
						path: "/api/user/notifications/read-all",
						description: "Mark all notifications as read",
						auth: "required",
					},
					{
						method: "POST",
						path: "/api/user/notifications/test",
						description: "Send test notification",
						auth: "required",
					},
				],
			},
			admin: {
				name: "Admin Management",
				endpoints: [
					{ method: "GET", path: "/api/admin/dashboard", description: "Get admin dashboard", auth: "admin" },
					{
						method: "GET",
						path: "/api/admin/campaigns/pending",
						description: "Get pending campaigns",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/campaigns/:id/approve",
						description: "Approve campaign",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/campaigns/:id/reject",
						description: "Reject campaign",
						auth: "admin",
					},
					{ method: "GET", path: "/api/admin/ads/pending", description: "Get pending ads", auth: "admin" },
					{ method: "POST", path: "/api/admin/ads/:id/approve", description: "Approve ad", auth: "admin" },
					{ method: "POST", path: "/api/admin/ads/:id/reject", description: "Reject ad", auth: "admin" },
					{
						method: "GET",
						path: "/api/admin/rejection-reasons",
						description: "Get rejection reasons",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/notifications",
						description: "Get admin notifications",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/recent-activity",
						description: "Get recent platform activity",
						auth: "admin",
					},
					{
						method: "PUT",
						path: "/api/admin/notifications/:id/read",
						description: "Mark notification as read",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/rate-limits/stats",
						description: "Get rate limit statistics",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/rate-limits/custom",
						description: "Set custom rate limit",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/rate-limits/reset",
						description: "Reset rate limit",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/rate-limits/status",
						description: "Get rate limit status",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/reports/daily",
						description: "Generate daily reports",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/reports/weekly",
						description: "Generate weekly reports",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/reports/monthly",
						description: "Generate monthly reports",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/campaigns/:id/trends",
						description: "Get campaign trends",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/reports/cleanup",
						description: "Cleanup old reports",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/fraud/statistics",
						description: "Get fraud statistics",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/fraud/analyses",
						description: "Get fraud analyses",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/fraud/analyze/click/:click_id",
						description: "Analyze click for fraud",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/fraud/analyze/impression/:impression_id",
						description: "Analyze impression for fraud",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/scheduler/status",
						description: "Get scheduler status",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/network/update",
						description: "Trigger network update",
						auth: "admin",
					},
					{
						method: "GET",
						path: "/api/admin/network/status",
						description: "Get network update status",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/scheduler/jobs/:job_name/stop",
						description: "Stop scheduler job",
						auth: "admin",
					},
					{
						method: "POST",
						path: "/api/admin/scheduler/jobs/:job_name/restart",
						description: "Restart scheduler job",
						auth: "admin",
					},
				],
			},
			network: {
				name: "Network & Analytics",
				endpoints: [
					{ method: "GET", path: "/api/subnets", description: "Get subnet information", auth: "optional" },
					{ method: "GET", path: "/api/validators", description: "Get validator data", auth: "optional" },
					{
						method: "GET",
						path: "/api/network-stats",
						description: "Get network statistics",
						auth: "optional",
					},
					{
						method: "GET",
						path: "/api/network-prices",
						description: "Get network price data",
						auth: "optional",
					},
				],
			},
			system: {
				name: "System & Monitoring",
				endpoints: [
					{ method: "GET", path: "/health", description: "Basic health check", auth: "optional" },
					{
						method: "GET",
						path: "/monitoring/health",
						description: "Detailed health metrics",
						auth: "optional",
					},
					{
						method: "GET",
						path: "/monitoring/metrics",
						description: "Application metrics",
						auth: "optional",
					},
					{
						method: "GET",
						path: "/monitoring/dashboard",
						description: "Monitoring dashboard",
						auth: "optional",
					},
				],
			},
		},
	};

	res.json(endpoints);
});

module.exports = router;
