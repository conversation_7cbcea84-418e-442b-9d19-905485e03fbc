// app/dashboard/ads/[id]/targeting/page.tsx
"use client";

import { CountrySelector } from "@/components/country-selector";
import { PageTypeSelector } from "@/components/page-type-selector";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Save } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Ad {
	id: number;
	campaign_id: number;
	title: string;
	status: string;
}

export default function AdTargetingPage() {
	const router = useRouter();
	const params = useParams();
	const { toast } = useToast();
	const adId = params.id as string;

	const [ad, setAd] = useState<Ad | null>(null);
	const [loading, setLoading] = useState(true);
	const [saving, setSaving] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Targeting state
	const [targetingMode, setTargetingMode] = useState<"all" | "include" | "exclude">("all");
	const [includedCountries, setIncludedCountries] = useState<string[]>([]);
	const [excludedCountries, setExcludedCountries] = useState<string[]>([]);
	const [selectedPageTypes, setSelectedPageTypes] = useState<string[]>(["all"]);
	const [selectedCategories, setSelectedCategories] = useState<Record<string, string[]>>({});

	useEffect(() => {
		if (adId) {
			fetchAdAndTargeting();
		}
	}, [adId]);

	const fetchAdAndTargeting = async () => {
		try {
			setLoading(true);
			
			// Fetch ad details
			const adResponse = await fetch(`/api/user/ads/${adId}`);
			if (!adResponse.ok) {
				throw new Error(`Failed to fetch ad: ${adResponse.status}`);
			}

			const adResult = await adResponse.json();
			if (adResult.success) {
				setAd(adResult.data);
			} else {
				throw new Error(adResult.message || "Failed to fetch ad");
			}

			// Fetch targeting data
			const targetingResponse = await fetch(`/api/user/ads/${adId}/targeting`);
			if (targetingResponse.ok) {
				const targetingResult = await targetingResponse.json();
				if (targetingResult.success && targetingResult.data) {
					const targeting = targetingResult.data;
					
					// Parse targeting data
					if (targeting.countries) {
						if (targeting.countries.mode === "include") {
							setTargetingMode("include");
							setIncludedCountries(targeting.countries.include || []);
						} else if (targeting.countries.mode === "exclude") {
							setTargetingMode("exclude");
							setExcludedCountries(targeting.countries.exclude || []);
						} else {
							setTargetingMode("all");
						}
					}

					if (targeting.pageTypes) {
						setSelectedPageTypes(targeting.pageTypes.types || ["all"]);
						setSelectedCategories(targeting.pageTypes.categories || {});
					}
				}
			}
		} catch (error) {
			console.error("Error fetching ad and targeting:", error);
			setError(error instanceof Error ? error.message : "Failed to fetch ad");
		} finally {
			setLoading(false);
		}
	};

	const handlePageTypeChange = (types: string[]) => {
		setSelectedPageTypes(types);
		
		// If "all" is selected, clear other selections
		if (types.includes("all")) {
			setSelectedPageTypes(["all"]);
			setSelectedCategories({});
		}
	};

	const handleSave = async () => {
		try {
			setSaving(true);

			// Prepare targeting data
			const targetingData = {
				countries: {
					mode: targetingMode,
					include: targetingMode === "include" ? includedCountries : [],
					exclude: targetingMode === "exclude" ? excludedCountries : [],
				},
				pageTypes: {
					types: selectedPageTypes,
					categories: selectedCategories,
				},
			};

			const response = await fetch(`/api/user/ads/${adId}/targeting`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(targetingData),
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Failed to update targeting: ${errorText}`);
			}

			const result = await response.json();
			if (result.success) {
				toast({
					title: "Targeting Updated",
					description: "Your ad targeting has been successfully updated.",
				});
				router.push(`/dashboard/campaigns/${ad?.campaign_id}`);
			} else {
				throw new Error(result.message || "Failed to update targeting");
			}
		} catch (error) {
			console.error("Error updating targeting:", error);
			toast({
				title: "Update Failed",
				description: error instanceof Error ? error.message : "Failed to update targeting",
				variant: "destructive",
			});
		} finally {
			setSaving(false);
		}
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
					<p className="text-sm text-muted-foreground">Loading targeting settings...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<p className="text-red-600 mb-4">{error}</p>
					<Button onClick={() => router.back()}>Go Back</Button>
				</div>
			</div>
		);
	}

	if (!ad) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<p className="text-gray-600 mb-4">Ad not found</p>
					<Button onClick={() => router.back()}>Go Back</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50 py-8">
			<div className="max-w-4xl mx-auto px-4">
				<div className="mb-6">
					<Button variant="ghost" onClick={() => router.back()} className="mb-4">
						<ArrowLeft className="mr-2 h-4 w-4" />
						Back to Campaign
					</Button>
					<div className="flex items-center justify-between">
						<div>
							<h1 className="text-3xl font-bold text-gray-900">Ad Targeting</h1>
							<p className="text-gray-600">Configure targeting settings for "{ad.title}"</p>
						</div>
					</div>
				</div>

				<div className="space-y-6">
					{/* Geographic Targeting */}
					<Card>
						<CardHeader>
							<CardTitle>Geographic Targeting</CardTitle>
							<CardDescription>
								Choose which countries should see your ad
							</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<RadioGroup
								value={targetingMode}
								onValueChange={(v) => setTargetingMode(v as "all" | "include" | "exclude")}
								className="flex flex-col space-y-2"
							>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="all" id="all" />
									<Label htmlFor="all">Show ads to all countries</Label>
								</div>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="include" id="include" />
									<Label htmlFor="include">Include specific countries only</Label>
								</div>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="exclude" id="exclude" />
									<Label htmlFor="exclude">Exclude specific countries</Label>
								</div>
							</RadioGroup>

							{targetingMode === "include" && (
								<div className="pl-6 space-y-2">
									<Label>Countries to include</Label>
									<CountrySelector
										selected={includedCountries}
										onChange={setIncludedCountries}
										placeholder="Select countries to include..."
									/>
								</div>
							)}

							{targetingMode === "exclude" && (
								<div className="pl-6 space-y-2">
									<Label>Countries to exclude</Label>
									<CountrySelector
										selected={excludedCountries}
										onChange={setExcludedCountries}
										placeholder="Select countries to exclude..."
									/>
								</div>
							)}
						</CardContent>
					</Card>

					<Separator />

					{/* Page Type Targeting */}
					<Card>
						<CardHeader>
							<CardTitle>Page Type Targeting</CardTitle>
							<CardDescription>
								Choose which types of pages should display your ad
							</CardDescription>
						</CardHeader>
						<CardContent>
							<PageTypeSelector
								selectedTypes={selectedPageTypes}
								selectedCategories={selectedCategories}
								onTypeChange={handlePageTypeChange}
								onCategoryChange={(type, cats) =>
									setSelectedCategories((prev) => ({ ...prev, [type]: cats }))
								}
							/>
						</CardContent>
					</Card>

					{/* Save Button */}
					<div className="flex justify-end">
						<Button onClick={handleSave} disabled={saving} className="min-w-[120px]">
							<Save className="mr-2 h-4 w-4" />
							{saving ? "Saving..." : "Save Targeting"}
						</Button>
					</div>
				</div>
			</div>
		</div>
	);
}
